// Player constants extracted from apps/web/src/constants/player.ts
// This file contains all player-related constants

export const DRM_PROVIDER = {
  SIGMA_DRM: 'sigmadrm'
};

export const ERROR_PLAYER = {
  SEVERITY: {
    RECOVERABLE: 1,
    CRITICAL: 2
  },
  CATEGORY: {
    NETWORK: 1,
    TEXT: 2,
    MEDIA: 3,
    MANIFEST: 4,
    STREAMING: 5,
    DRM: 6,
    PLAYER: 7,
    CAST: 8,
    STORAGE: 9,
    ADS: 10
  },
  TYPE: {
    PLAYER: 'PLAYER',
    API_DETAIL: 'API_DETAIL',
    EMPTY_LINK: 'EMPTY_LINK',
    QNET: 'QNET',
    VALIDATE_KPLUS: 'VALIDATE_KPLUS',
    SOCKET: 'SOCKET',
    UNDEFINED: 'UNDEFINED'
  }
};

export const HLS_CONFIG = {
  maxBufferLength: 24,
  maxMaxBufferLength: 200,
  maxBufferSize: 20 * 1000 * 1000,
  maxLoadingDelay: 0,
  manifestLoadingTimeOut: 5000, // time out
  manifestLoadingMaxRetry: 2, // retry
  manifestLoadingRetryDelay: 1000,
  manifestLoadingMaxRetryTimeout: 64000,
  levelLoadingTimeOut: 10000,
  levelLoadingMaxRetry: 3,
  levelLoadingRetryDelay: 1000,
  levelLoadingMaxRetryTimeout: 64000,
  nudgeMaxRetry: 6,
  enableWebVTT: true,
  subtitleDisplay: false
};

export const DRM = {
  CASTLAB: 'CASTLAB',
  QNET: 'QNET',
  K_PLUS: 'K+',
  HBO: 'HBO',
  VieON: 'vieon',
  APP_ID: 'vieon'
};

export const QNET = {
  USER_ID: '7-926dbdfbeee346e692c1d27aece20131',
  SESSION_ID:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySUQiOiI3LTkyNmRiZGZiZWVlMzQ2ZTY5MmMxZDI3YWVjZTIwMTMxIiwic2Vzc2lvbklkIjoiZGZnZGZnZGZnZGYiLCJ0aW1lc3RhbXAiOiIxNTgzMjA1MjMxIn0.dUbMaENNRRDb5WcIFqainSrvQQZWgv9MIytk-KSySvo',
  MERCHANT: 'qnet'
};

export const PLAYER_NAME = {
  SHAKA_PLAYER: 'SHAKA_PLAYER',
  HLS_PLAYER: 'HLS_PLAYER'
};

export const PLAYER_TYPE = {
  MASTER_BANNER: 'MASTER_BANNER',
  BANNER_COLLECTION: 'BANNER_COLLECTION',
  BANNER: 'BANNER',
  CARD_DETAIL: 'CARD_DETAIL',
  CARD_HOVER: 'CARD_HOVER',
  CARD_AIRING: 'CARD_AIRING',
  END_SCREEN_TRAILER: 'END_SCREEN_TRAILER',
  VOD: 'VOD'
};

export const PLAYER_STATUS = {
  PLAYING: 1,
  ENDED: 2,
  ERROR: 3,
  PAUSED: 4,
  WAITING: 5,
  CLEAR: 6
};

// DRM Certificate constants (these would be set from environment variables in actual usage)
export const DRM_CERT = {
  DRM_TODAY_FAIR_PLAY: '', // Will be set from env
  SIGMA_FAIR_PLAY: '' // Will be set from env
};

// DRM Server constants (these would be set from environment variables in actual usage)
export const DRM_SERVER = {
  TODAY_WIDE_VINE: '', // Will be set from env
  TODAY_FAIR_PLAY: '', // Will be set from env
  SIGMA_WIDE_VINE: '', // Will be set from env
  SIGMA_FAIR_PLAY: '', // Will be set from env
  SIGMA_PLAY_READY: '' // Will be set from env
};

// Player quality constants
export const PLAYER_QUALITY = {
  AUTO: 'auto',
  SD: 'sd',
  HD: 'hd',
  FULL_HD: 'full_hd',
  UHD: 'uhd'
};

// Player mode constants
export const PLAYER_MODE = {
  NORMAL: 'normal',
  FULLSCREEN: 'fullscreen',
  MINI: 'mini',
  THEATER: 'theater'
};

// Subtitle constants
export const SUBTITLE = {
  OFF: 'off',
  VIETNAMESE: 'vi',
  ENGLISH: 'en'
};

// Audio track constants
export const AUDIO_TRACK = {
  ORIGINAL: 'original',
  VIETNAMESE: 'vi',
  ENGLISH: 'en'
};

// Player event constants
export const PLAYER_EVENT = {
  PLAY: 'play',
  PAUSE: 'pause',
  ENDED: 'ended',
  ERROR: 'error',
  LOADING: 'loading',
  LOADED: 'loaded',
  TIME_UPDATE: 'timeupdate',
  VOLUME_CHANGE: 'volumechange',
  FULLSCREEN_CHANGE: 'fullscreenchange',
  QUALITY_CHANGE: 'qualitychange',
  SUBTITLE_CHANGE: 'subtitlechange',
  AUDIO_CHANGE: 'audiochange'
};

// Player control constants
export const PLAYER_CONTROL = {
  PLAY_PAUSE: 'play_pause',
  VOLUME: 'volume',
  PROGRESS: 'progress',
  FULLSCREEN: 'fullscreen',
  QUALITY: 'quality',
  SUBTITLE: 'subtitle',
  AUDIO: 'audio',
  SPEED: 'speed'
};

// Playback speed constants
export const PLAYBACK_SPEED = {
  SLOW: 0.5,
  NORMAL: 1,
  FAST: 1.25,
  FASTER: 1.5,
  FASTEST: 2
};

// Player skin constants
export const PLAYER_SKIN = {
  DEFAULT: 'default',
  MINIMAL: 'minimal',
  CUSTOM: 'custom'
};

// Buffer constants
export const BUFFER = {
  MIN_BUFFER_TIME: 2,
  MAX_BUFFER_TIME: 30,
  BUFFER_AHEAD: 10
};

// Seek constants
export const SEEK = {
  FORWARD: 10,
  BACKWARD: 10,
  CHAPTER_FORWARD: 30,
  CHAPTER_BACKWARD: 30
};

// Player analytics constants
export const PLAYER_ANALYTICS = {
  PLAY_START: 'play_start',
  PLAY_END: 'play_end',
  PAUSE: 'pause',
  RESUME: 'resume',
  SEEK: 'seek',
  QUALITY_CHANGE: 'quality_change',
  ERROR: 'error',
  BUFFER_START: 'buffer_start',
  BUFFER_END: 'buffer_end'
};
