// Type constants extracted from apps/web/src/constants/types.ts
// This file contains all type-related constants

export const TYPE_TRIGGER_AUTH = {
  ADD_TO_LIST: 'ADD_TO_LIST',
  REMIND_ME: 'REMIND_ME',
  COMMENT: 'COMMENT',
  RECOMMENDATION: 'RECOMMENDATION',
  RATING: 'RATING',
  LIVESTREAM_COMING_SOON: 'LIVESTREAM_COMING_SOON',
  LIVE_TV_COMING_SOON: 'LIVE_TV_COMING_SOON',
  REPORT: 'REPORT',
  PAYMENT: 'PAYMENT',
  PAYMENT_LOGIN: 'PAYMENT_LOGIN',
  PAYMENT_TVOD: 'PAYMENT_TVOD',
  LOGIN_DEVICE: 'LOGIN_DEVICE',
  INPUT_VOUCHER: 'INPUT_VOUCHER',
  INPUT_PROMOTION: 'INPUT_PROMOTION',
  SETTING: 'SETTING',
  CONTENT: 'CONTENT',
  PROMOTION: 'PROMOTION',
  SOCIAL_UPDATE_PHONE: 'SOCIAL_UPDATE_PHONE',
  PROFILE_UPDATE_PHONE: 'PROFILE_UPDATE_PHONE',
  PAYMENT_BUY_PACKAGE: 'PAYMENT_BUY_PACKAGE',
  PAYMENT_BUY_PACKAGE_PRE_ORDER: 'PAYMENT_BUY_PACKAGE_PRE_ORDER',
  REQUEST_REGISTER_CONVERSION: 'REQUEST_REGISTER_CONVERSION'
};

export const TYPE_INPUT = {
  TEL: 'tel',
  TEXT: 'text',
  PASSWORD: 'password',
  EMAIL: 'email',
  NUMBER: 'number',
  SEARCH: 'search',
  URL: 'url',
  DATE: 'date',
  TIME: 'time',
  DATETIME: 'datetime-local',
  CHECKBOX: 'checkbox',
  RADIO: 'radio',
  SELECT: 'select',
  TEXTAREA: 'textarea',
  FILE: 'file',
  HIDDEN: 'hidden'
};

export const TYPE_RECEIVE_OTP = {
  EMAIL: 1,
  PHONE: 2
};

export const TYPE_AUTH = {
  REGISTER: 'register',
  FORGOT_PASSWORD: 'forgot_password',
  RESET_PASSWORD: 'reset_password',
  BIND_PHONE_OTP: 'bind_phone_otp',
  LINK_PHONE_NUMBER_OTP: 'link_phone_number_otp',
  UPDATE_PASSWORD_OTP: 'update_password_otp',
  RESTORE_ACCOUNT_OTP: 'restore_account_otp'
};

export const LOGIN_INPUT_TYPE = {
  PHONE: 'phone',
  EMAIL: 'email',
  UNKNOWN: 'unknown'
};

export const INVOICE_INPUT_TYPE = {
  UPDATE_COMPANY_NAME: 'companyName',
  UPDATE_COMPANY_TIN: 'taxCode',
  UPDATE_COMPANY_ADDRESS: 'address',
  UPDATE_INVOICE_EMAIL: 'email'
};

export const OTP_TYPE = {
  SMS: 'sms',
  ZNS: 'zns',
  MAIL: 'mail',
  NONE: 'none'
};

// Content type constants
export const CONTENT_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  ARCHIVED: 'archived',
  DELETED: 'deleted',
  SCHEDULED: 'scheduled'
};

export const CONTENT_VISIBILITY = {
  PUBLIC: 'public',
  PRIVATE: 'private',
  UNLISTED: 'unlisted',
  PREMIUM: 'premium'
};

// User type constants
export const USER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
  BANNED: 'banned',
  PENDING: 'pending'
};

export const USER_ROLE = {
  ADMIN: 'admin',
  MODERATOR: 'moderator',
  USER: 'user',
  GUEST: 'guest',
  VIP: 'vip',
  PREMIUM: 'premium'
};

// Device type constants
export const DEVICE_CATEGORY = {
  MOBILE: 'mobile',
  TABLET: 'tablet',
  DESKTOP: 'desktop',
  TV: 'tv',
  CONSOLE: 'console',
  UNKNOWN: 'unknown'
};

export const DEVICE_OS = {
  ANDROID: 'android',
  IOS: 'ios',
  WINDOWS: 'windows',
  MACOS: 'macos',
  LINUX: 'linux',
  TIZEN: 'tizen',
  WEBOS: 'webos',
  UNKNOWN: 'unknown'
};

// Media type constants
export const MEDIA_TYPE = {
  VIDEO: 'video',
  AUDIO: 'audio',
  IMAGE: 'image',
  DOCUMENT: 'document',
  SUBTITLE: 'subtitle',
  THUMBNAIL: 'thumbnail'
};

export const VIDEO_FORMAT = {
  MP4: 'mp4',
  HLS: 'hls',
  DASH: 'dash',
  WEBM: 'webm',
  AVI: 'avi',
  MOV: 'mov'
};

export const AUDIO_FORMAT = {
  MP3: 'mp3',
  AAC: 'aac',
  WAV: 'wav',
  FLAC: 'flac',
  OGG: 'ogg'
};

export const IMAGE_FORMAT = {
  JPEG: 'jpeg',
  JPG: 'jpg',
  PNG: 'png',
  WEBP: 'webp',
  GIF: 'gif',
  SVG: 'svg'
};

// Notification type constants
export const NOTIFICATION_TYPE = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
  PROMOTION: 'promotion',
  SYSTEM: 'system',
  CONTENT: 'content',
  PAYMENT: 'payment'
};

export const NOTIFICATION_STATUS = {
  UNREAD: 'unread',
  READ: 'read',
  ARCHIVED: 'archived',
  DELETED: 'deleted'
};

// Analytics type constants
export const ANALYTICS_EVENT = {
  PAGE_VIEW: 'page_view',
  CLICK: 'click',
  SCROLL: 'scroll',
  FORM_SUBMIT: 'form_submit',
  VIDEO_PLAY: 'video_play',
  VIDEO_PAUSE: 'video_pause',
  VIDEO_END: 'video_end',
  SEARCH: 'search',
  DOWNLOAD: 'download',
  SHARE: 'share'
};

export const ANALYTICS_CATEGORY = {
  USER: 'user',
  CONTENT: 'content',
  PAYMENT: 'payment',
  NAVIGATION: 'navigation',
  INTERACTION: 'interaction',
  PERFORMANCE: 'performance',
  ERROR: 'error'
};

// API response type constants
export const API_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  LOADING: 'loading',
  IDLE: 'idle'
};

export const API_ERROR_TYPE = {
  NETWORK: 'network',
  SERVER: 'server',
  CLIENT: 'client',
  TIMEOUT: 'timeout',
  VALIDATION: 'validation',
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization'
};

// Form validation type constants
export const VALIDATION_TYPE = {
  REQUIRED: 'required',
  EMAIL: 'email',
  PHONE: 'phone',
  URL: 'url',
  NUMBER: 'number',
  MIN_LENGTH: 'min_length',
  MAX_LENGTH: 'max_length',
  PATTERN: 'pattern',
  CUSTOM: 'custom'
};

// Storage type constants
export const STORAGE_TYPE = {
  LOCAL: 'local',
  SESSION: 'session',
  COOKIE: 'cookie',
  INDEXED_DB: 'indexed_db'
};

// Theme type constants
export const THEME_TYPE = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
};

// Language type constants
export const LANGUAGE_TYPE = {
  VIETNAMESE: 'vi',
  ENGLISH: 'en',
  AUTO: 'auto'
};

// Sort type constants
export const SORT_TYPE = {
  ASC: 'asc',
  DESC: 'desc'
};

export const SORT_BY = {
  NAME: 'name',
  DATE: 'date',
  POPULARITY: 'popularity',
  RATING: 'rating',
  VIEWS: 'views',
  DURATION: 'duration',
  PRICE: 'price'
};

// Filter type constants
export const FILTER_TYPE = {
  CATEGORY: 'category',
  GENRE: 'genre',
  YEAR: 'year',
  RATING: 'rating',
  DURATION: 'duration',
  PRICE: 'price',
  QUALITY: 'quality',
  LANGUAGE: 'language'
};

// Layout type constants
export const LAYOUT_TYPE = {
  GRID: 'grid',
  LIST: 'list',
  CARD: 'card',
  TABLE: 'table'
};

// Modal type constants
export const MODAL_TYPE = {
  DIALOG: 'dialog',
  POPUP: 'popup',
  DRAWER: 'drawer',
  FULLSCREEN: 'fullscreen'
};

// Button type constants
export const BUTTON_TYPE = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info',
  LINK: 'link',
  GHOST: 'ghost'
};

// Size type constants
export const SIZE_TYPE = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large',
  EXTRA_LARGE: 'extra_large'
};
