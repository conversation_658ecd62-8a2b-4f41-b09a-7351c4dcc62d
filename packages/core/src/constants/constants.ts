// Core constants extracted from apps/web/src/constants/constants.ts
// This file contains all shared constants that can be used across multiple applications

export const DOMAIN_WEB = 'https://vieon.vn';

export const LOGIN_TYPE = {
  PHONE: 'phone',
  EMAIL: 'email',
  FACEBOOK: 'facebook',
  GOOG<PERSON>: 'google',
  APPLE: 'apple'
};

export const FLOW_GLOBAL_AUTH = {
  REGISTER: 'register',
  LOGIN: 'login',
  FORGOT_PASSWORD: 'forgot_password',
  RESET_PASSWORD: 'reset_password',
  BIND_PHONE: 'bind_phone',
  BIND_PHONE_OTP: 'bind_phone_otp',
  BIND_PHONE_SET_PASS: 'bind_phone_set_pass',
  LINK_PHONE_NUMBER: 'link_phone_number',
  LINK_PHONE_NUMBER_OTP: 'link_phone_number_otp',
  LINK_PHONE_NUMBER_SET_PASS: 'link_phone_number_set_pass'
};

export const GG_LIBRARY = 'https://accounts.google.com/gsi/client';
export const KEY_NAPAS_RESULT_WEB = 'napas_result_web';
export const KEY_NAPAS_RESULT_SMART_TV = 'napas_result_smart_tv';

export const PLATFORM = {
  WEB: 'web',
  MOBILE_WEB: 'mobile_web',
  SMART_TV: 'smart_tv',
  TABLET_WEB: 'tablet_web',
  SMART_TV_WEB: 'smarttv_web'
};

export const TIME_RESEND_OTP = 60;
export const ASK_LOGIN = 'ASK_LOGIN';
export const PRICE_GIFT_CODE_DEFAULT = 30000;

export const LOBBY = {
  AGE_RANGES: 'age_ranges',
  KID_AGE_RANGES: 'kid_age_ranges',
  GENDERS: 'genders'
};

export const BROWSER = {
  CHROME: 'Chrome',
  FIREFOX: 'Firefox',
  SAFARI: 'Safari',
  EDGE: 'Edge',
  IE: 'Internet Explorer',
  OPERA: 'Opera'
};

export const DEVICE_TYPE = {
  MOBILE: 'mobile',
  TABLET: 'tablet',
  DESKTOP: 'desktop',
  SMART_TV: 'smart_tv'
};

export const HTTP_CODE = {
  SUCCESS: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
};

export const API_METHOD = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH'
};

export const ERROR_CODE = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  INTERNAL_ERROR: 'INTERNAL_ERROR'
};

export const PAGE = {
  HOME: 'home',
  DETAIL: 'detail',
  SEARCH: 'search',
  PROFILE: 'profile',
  PAYMENT: 'payment',
  LIVE_TV: 'live_tv',
  LIVE_STREAM: 'live_stream'
};

export const PAGE_MAX_SIZE = 50;

export const BANNER_IMAGE_RATE = {
  RATE_16_9: '16:9',
  RATE_4_3: '4:3',
  RATE_1_1: '1:1'
};

export const EL_ID = {
  HEADER: 'header',
  FOOTER: 'footer',
  MAIN: 'main',
  SIDEBAR: 'sidebar'
};

export const ACTION_TYPE_NOTIFY = {
  MARK_READ: 'mark_read',
  MARK_ALL_READ: 'mark_all_read'
};

export const KEY_TIME_NOTIFY = {
  TO_DAY: 0,
  YESTERDAY: 1,
  OLD_DAYS: 2
};

export const MATCH_STATUS = {
  SCHEDULE: 'SCHEDULE',
  FINISHED: 'FINISHED',
  LIVE: 'LIVE',
  CANCELED: 'CANCELED',
  SUSPENDED: 'SUSPENDED'
};

export const RIBBON_TYPE = {
  NORMAL: 0,
  PROMOTION_RIBBON: 1,
  PROMOTED_RIBBON_ADS: 2,
  TOP_VIEWS: 3,
  ORIGINAL: 4,
  LIVE_TV_FAVORITE: 5,
  LIVE_TV_CATEGORY: 6,
  LIVE_TV_SCHEDULE: 7,
  LIVE_TV_SEARCH: 8,
  SEARCH_RESULT: 9,
  ARTIST_FILMOGRAPHY: 10,
  COLLECTION_DETAIL: 11,
  RELATED_CONTENT: 12,
  EPISODE_LIST: 13,
  SEASON_LIST: 14,
  TRAILER_LIST: 15,
  RECOMMENDATION: 16,
  TRENDING: 17,
  NEW_RELEASE: 18,
  COMING_SOON: 19,
  CONTINUE_WATCHING: 20
};

export const SEASON_CATEGORY = {
  ALL: 'all',
  MOVIE: 'movie',
  SERIES: 'series',
  DOCUMENTARY: 'documentary',
  VARIETY: 'variety',
  KIDS: 'kids'
};

export const USER_TYPE = {
  NON_VIP: 0,
  VIP: 1,
  VIP_K_PLUS: 2,
  VIP_HBO: 3,
  ALL_ACCESS: 4
};

export const USER_TYPE_ENUM = {
  GUEST: 'Guest',
  NON_VIP: 'Non VIP',
  VIP: 'VIP',
  HBO: 'HBO',
  K_PLUS: 'K+',
  ALL_ACCESS: 'All Access'
};

export const REGEX = {
  VN_PHONE_NUMBER: /^(0\d{9}|[1-9]\d{8})$/,
  PHONE_NUMBER: /^0[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s./0-9]*$/,
  NUMBER: /^[0-9\b]+$/,
  NOT_CHAR_SPECIAL: /[`!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?~]/,
  EMAIL:
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
};

export const RESOLUTION = {
  SD: 1,
  HD: 2,
  FULL_HD: 3,
  UHD: 4
};

export const CURRENCY = {
  VND: 'VND',
  D: 'đ'
};

export const KEY_CODE = {
  ESC: 27,
  SPACE: 32,
  ARROW_LEFT: 37,
  ARROW_RIGHT: 39,
  BACK_SPACE: 8
};

export const UTM_SOURCE = {
  TP_BANK: 'tpbank',
  ZALO_PAY: 'zalopay',
  ADBRO: 'Adbro'
};

export const MKT_PARAMS = [
  'af_force_deeplink',
  'fbclid',
  'pid',
  'utm_source',
  'utm_medium',
  'af_adset',
  'af_ad',
  'utm_campaign',
  'c'
];

export const LIMIT_DATA = 10;
export const PACKAGE_INTRO_LENGTH = 150;
export const DAY_SECOND = 24 * 60 * 60;

export const VieON = {
  SUPPORT_EMAIL: '<EMAIL>',
  HOTLINE: '1800 599920'
};

export const LOCATION = {
  VIETNAM: 'VN'
};

export const NODE_ENV = {
  DEV: 'development',
  PROD: 'production'
};

export const WEB_ENV = {
  DEV: 'develop',
  TESTING: 'testing',
  STAGING: 'staging',
  PRODUCTION: 'production'
};

export const TIMER = {
  UNDER_CONSTRUCTION: 30000
};

export const ON_OFF = {
  ON: 'on'
};

// Content related constants
export const CONTENT_TYPE: any = {
  LIVESTREAM: 0,
  SEASON: 3,
  EPISODE: 4,
  LIVE_TV: 5,
  MOVIE: 1,
  EPG: 7,
  TRAILER: 6,
  RIBBON: 10,
  MASK_ID: 'mask-id',
  ADS: 'banner-ads',
  BANNER_TRIGGER: 'banner-trigger',
  SHORT_CONTENT: 4
};

export const PREMIUM_TYPE = {
  AVOD: 0,
  SVOD: 1,
  TVOD: 5,
  PVOD_6: 6,
  PVOD_7: 7,
  SVOD_TVOD: 8
};

export const TVOD_TYPE = {
  RENTAL: 2,
  PRE_ORDER_LIVE_EVENT: 3,
  PRE_ORDER_SIMULCAST: 4
};

export const TVOD = {
  TYPE: {
    MOVIE: 'MOVIE',
    TV_SERIES: 'TV_SERIES',
    RIBBON: 'RIBBON',
    LIVE_EVENT: 'LIVE_EVENT',
    SIMULCAST_SINGLE: 'SIMULCAST_SINGLE',
    SIMULCAST_MULTI: 'SIMULCAST_MULTI'
  },
  USER_TYPE: {
    EXPIRED: -1, // Hết hạn
    NONE: 0, // Chưa mua
    RENTED: 1, // Đã thuê
    WATCHED: 2 // Đã xem
  },
  ID_TYPE: {
    MOVIE: 'content',
    SEASON: 'season',
    EPISODE: 'episode'
  },
  TOAST: {
    EVENT_HAPPENING_REMINDER: 'event_happening_reminder'
  },
  POPUP: {
    TIME_OUT_SALE_PRE_ORDER_HAVE_VOD: 'time_out_sale_pre_order_have_vod',
    TIME_OUT_SALE_PRE_ORDER: 'time_out_sale_pre_order'
  }
};

// Payment related constants
export const PAYMENT_METHOD = {
  NAPAS: 'napas',
  ASIAPAY: 'asiapay',
  VN_PAY: 'vnpay',
  MOMO: 'momo',
  MOCA: 'moca',
  MOBI: 'mobiphone',
  VINA: 'vinaphone',
  ZALO_PAY: 'zalopay',
  SHOPEE_PAY: 'shopeepay',
  VIETTEL: 'viettel',
  PAYOO: 'payoo',
  IAP: 'IAP',
  TP_BANK: 'tpbank',
  VIETTEL_PAY: 'viettelpay',
  QRCODE: 'qrcode',
  CAKE: 'cake',
  QR_VNPAY: 'qr_vnpay'
};

export const PAID_TYPE = {
  OVERLAP: 2,
  ACCUMULATE: 1
};

// Permission constants
export const PERMISSION = {
  NON_LOGIN: 0,
  CAN_WATCH: 206, // can watch permission
  GIFT_CODE: 207,
  PAYMENT: 208, // chưa mua gói
  DONT_ALLOW_BROADCAST: 405,
  LIMITED_DEVICE: 429, // giới hạn tb
  FORCE_LOGIN: 1,
  KID_LIMITED: 145,
  CONTENT_RESCTRICTED: 146
};

// Position trigger constants
export const POSITION_TRIGGER = {
  SEARCH: 'search',
  NOTIFY: 'notification',
  VIDEO_INTRO: 'video intro',
  PROFILE: 'profile',
  LIVE_TV: 'livetv',
  COMING_SOON: 'coming soon'
};

// Link play constants
export const LINK_PLAY_KEY = {
  HLS: 'hlsLinkPlay',
  HLS_BK1: 'hlsBackup1',
  HLS_BK2: 'hlsBackup2',
  DASH: 'dashLinkPlay',
  DASH_BK1: 'dashBackup1',
  DASH_BK2: 'dashBackup2',
  ERROR: 'ERROR'
};

// Ranking constants
export const RANKING_TAB = {
  SCHEDULE: 'SCHEDULE',
  RANKING: 'RANKING'
};

// Sentry constants
export const SENTRY = {
  PAYMENT: 'PAYMENT',
  API: 'API',
  PLAYER: 'PLAYER',
  PROFILE: 'PROFILE',
  CREATE_TRANSACTION: 'CREATE_TRANSACTION'
};

// Ads constants
export const STATUS_OUTSTREAM_ADS = {
  FAIL: 'fail',
  CLOSE: 'close',
  SUCCESS: 'success'
};

export const AD_TYPE = {
  OVERLAY: 'overlay',
  PREROLL: 'preroll',
  MIDROLL: 'midroll',
  POSTROLL: 'postroll'
};

// Player tip constants
export const PLAYER_TIP_STATUS = {
  INIT: 'INIT',
  COLLAPSE: 'COLLAPSE',
  EXPAND: 'EXPAND'
};

// Domain API SSR constants
export const DOMAIN_API_SSR = {
  CM_V5: 'api.vieon.vn/backend/cm/v5',
  BACKEND_USER: 'api.vieon.vn/backend/user',
  CM_ACTIVITY: 'api.vieon.vn/backend/cm/activity',
  USER_REPORT: 'api.vieon.vn/backend/user-report',
  SHOP_BACKEND_API: 'api.vieon.vn/shop-backend-api'
};
