// VieON Core Package
// This package contains shared utilities, constants, types, and configurations
// that can be used across multiple VieON applications

// Export all constants
export * from './constants';

// Export all helpers
export * from './helpers';

// Export all config utilities
export * from './config';

// Export types (excluding conflicting ones)
export * from './types/common';
export * from './types/models';

// Package information
export const PACKAGE_INFO = {
  name: '@vieon/core',
  version: '1.0.0',
  description: 'Core utilities, constants, helpers, and types for VieON applications',
  author: 'VieON Team',
  license: 'MIT'
};

// Re-export commonly used utilities for convenience
export {
  // Common helpers
  formatDate,
  formatDateTime,
  formatNumber,
  formatCurrency,
  formatPhoneNumberVN,
  truncate,
  capitalize,
  
  // Storage utilities
  setLocalStorage,
  getLocalStorage,
  removeLocalStorage,
  setSessionStorage,
  getSessionStorage,
  
  // URL utilities
  getQueryParam,
  buildQueryString,
  addParamToUrlVieON,
  
  // Time utilities
  getLiveTime,
  checkTimeLive,
  parseRemainText,
  
  // Browser utilities
  getBrowser,
  detectWebViewUA,
  
  // Validation utilities
  isValidEmail,
  isValidPhone,
  
  // Array utilities
  chunk,
  unique,
  shuffle,
  groupBy,
  
  // Object utilities
  pick,
  omit,
  deepClone,
  isEmpty,
  
  // Type guards
  isString,
  isNumber,
  isArray,
  isObject,
  isFunction,
  isBoolean,
  isDate,
  
  // Async utilities
  sleep,
  retry,
  timeout,
  debounce,
  throttle,
  
  // Math utilities
  clamp,
  random,
  round,
  percentage,
  
  // Device utilities
  isMobile,
  isTablet,
  isDesktop,
  
  // Cookie utilities
  setCookie,
  getCookie,
  deleteCookie,
} from './helpers';

export {
  // Environment utilities
  getEnvConfig,
  isDevelopment,
  isProduction,
  isStaging,
  isTesting,
  getApiConfig,
  getCdnConfig,
  getDomainConfig,
  getFeatureFlags,
  buildApiUrl,
  buildCdnUrl,
  buildAppUrl,
  
  // Storage utilities
  LOCAL_STORAGE_KEYS,
  SESSION_STORAGE_KEYS,
  COOKIE_KEYS,
  STORAGE_CONFIG,
  createStorageKey,
  isStorageAvailable,
  getStorageSize,
  clearExpiredItems,
  cleanupStorage,
  clearAllVieOnStorage,
} from './config';

export {
  // Core constants
  DOMAIN_WEB,
  LOGIN_TYPE,
  PLATFORM,
  BROWSER,
  DEVICE_TYPE,
  HTTP_CODE,
  API_METHOD,
  ERROR_CODE,
  USER_TYPE,
  USER_TYPE_ENUM,
  CONTENT_TYPE,
  PREMIUM_TYPE,
  PAYMENT_METHOD,
  REGEX,
  CURRENCY,
  
  // Player constants
  PLAYER_NAME,
  PLAYER_TYPE,
  PLAYER_STATUS,
  HLS_CONFIG,
  DRM,
  
  // Text constants
  TEXT,
  
  // Type constants
  TYPE_TRIGGER_AUTH,
  TYPE_INPUT,
  TYPE_RECEIVE_OTP,
  TYPE_AUTH,
  LOGIN_INPUT_TYPE,
} from './constants';

// Default export for convenience
export default {
  constants: require('./constants'),
  helpers: require('./helpers'),
  types: require('./types'),
  config: require('./config'),
  version: PACKAGE_INFO.version,
};
