// Common helpers extracted from apps/web/src/helpers/common.ts
// This file contains all shared helper functions

import moment from 'moment';
import Bowser from 'bowser';
import CryptoJS from 'crypto-js';
// Remove unused lodash imports for now

// Browser detection utilities
export const getBrowser = () => {
  if (typeof window !== 'undefined') {
    const browser = Bowser.getParser(window.navigator.userAgent);
    return browser.getBrowser();
  }
  return null;
};

export const detectWebViewUA = () => {
  if (typeof window === 'undefined') return false;

  const userAgent = window.navigator.userAgent;
  const isWebView = /wv|WebView|(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(userAgent);
  return isWebView;
};

// Date/time utilities
export const formatDate = (date: Date | string, format = 'DD/MM/YYYY'): string => {
  return moment(date).format(format);
};

export const formatDateTime = (date: Date | string, format = 'DD/MM/YYYY HH:mm'): string => {
  return moment(date).format(format);
};

export const getLiveTime = (timestamp: any, isNotify?: any) => {
  if (!timestamp) return '';
  if (timestamp.toString().length > 10) {
    timestamp = Math.floor(timestamp / 1000);
  }

  const now = Math.floor(new Date().getTime() / 1000);
  const diff = now - timestamp;

  if (diff < 60) {
    return isNotify ? 'Vừa xong' : 'Vài giây trước';
  } else if (diff < 3600) {
    const minutes = Math.floor(diff / 60);
    return `${minutes} phút trước`;
  } else if (diff < 86400) {
    const hours = Math.floor(diff / 3600);
    return `${hours} giờ trước`;
  } else if (diff < 2592000) {
    const days = Math.floor(diff / 86400);
    return `${days} ngày trước`;
  } else if (diff < 31536000) {
    const months = Math.floor(diff / 2592000);
    return `${months} tháng trước`;
  } else {
    const years = Math.floor(diff / 31536000);
    return `${years} năm trước`;
  }
};

export const checkTimeLive = (data: any) => {
  const nowTime = Math.floor(new Date().getTime() / 1000);
  const checkTimeLive = data - nowTime <= 0;
  return checkTimeLive;
};

// String utilities
export const truncate = (str: string, length: number, suffix = '...'): string => {
  if (!str || str.length <= length) return str;
  return str.slice(0, length - suffix.length) + suffix;
};

export const capitalize = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const capitalizeFirstLetter = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const parseUrlString = (str: any, key?: any, value?: any, isCapitalizeFirstLetter?: any) => {
  let newStr = str;
  const myKey = `{${key}}`;
  if (str && key) {
    newStr = newStr.replace(
      isCapitalizeFirstLetter ? capitalizeFirstLetter(myKey || '') : myKey,
      value || ''
    );
  }
  return newStr;
};

// Number utilities
export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('vi-VN').format(num);
};

export const numberWithCommas = (num: any): string => {
  if (!num && num !== 0) return '';
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

export const formatCurrency = (amount: number, currency = 'VND'): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

// Phone number utilities
export const formatPhoneNumberVN = (phoneNumber: string): string => {
  if (!phoneNumber) return '';

  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');

  // Check if it's a Vietnamese phone number
  if (cleaned.length === 10 && cleaned.startsWith('0')) {
    // Format: 0xxx xxx xxx
    return cleaned.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
  } else if (cleaned.length === 9) {
    // Format: xxx xxx xxx (without leading 0)
    return cleaned.replace(/(\d{3})(\d{3})(\d{3})/, '$1 $2 $3');
  }

  return phoneNumber;
};

// URL utilities
export const getQueryParam = (param: string, url?: string): string | null => {
  const searchParams = new URLSearchParams(url || window.location.search);
  return searchParams.get(param);
};

export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      searchParams.append(key, String(value));
    }
  });

  return searchParams.toString();
};

export const getUrlFromPath = (path: string, baseUrl?: string): string => {
  if (!path) return '';

  const base = baseUrl || (typeof window !== 'undefined' ? window.location.origin : '');

  if (path.startsWith('http')) {
    return path;
  }

  return `${base}${path.startsWith('/') ? '' : '/'}${path}`;
};

export const parseQueryString = (queryString: string): Record<string, string> => {
  const params: Record<string, string> = {};
  const searchParams = new URLSearchParams(queryString);

  searchParams.forEach((value, key) => {
    params[key] = value;
  });

  return params;
};

export const addParamToUrlVieON = (url: string, params: Record<string, any> = {}): string => {
  if (!url) return '';

  const [baseUrl, hash] = url.split('#');
  const [path, query] = (baseUrl || '').split('?');

  const searchParams = new URLSearchParams(query || '');

  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      searchParams.set(key, String(value));
    }
  });

  const newQuery = searchParams.toString();
  const newUrl = path + (newQuery ? `?${newQuery}` : '') + (hash ? `#${hash}` : '');

  return newUrl;
};

// Storage utilities
export const setLocalStorage = (key: string, value: any): void => {
  if (typeof window !== 'undefined') {
    try {
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (e) {
      console.error('Failed to set localStorage', e);
    }
  }
};

export const getLocalStorage = (key: string): any => {
  if (typeof window !== 'undefined') {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (e) {
      console.error('Failed to get localStorage', e);
      return null;
    }
  }
  return null;
};

export const removeLocalStorage = (key: string): void => {
  if (typeof window !== 'undefined') {
    try {
      window.localStorage.removeItem(key);
    } catch (e) {
      console.error('Failed to remove localStorage', e);
    }
  }
};

export const setSessionStorage = (key: string, value: any): void => {
  if (typeof window !== 'undefined') {
    try {
      window.sessionStorage.setItem(key, JSON.stringify(value));
    } catch (e) {
      console.error('Failed to set sessionStorage', e);
    }
  }
};

export const getSessionStorage = (key: string): any => {
  if (typeof window !== 'undefined') {
    try {
      const item = window.sessionStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (e) {
      console.error('Failed to get sessionStorage', e);
      return null;
    }
  }
  return null;
};

export const removeSessionStorage = (key: string): void => {
  if (typeof window !== 'undefined') {
    try {
      window.sessionStorage.removeItem(key);
    } catch (e) {
      console.error('Failed to remove sessionStorage', e);
    }
  }
};

// Timer utilities
export const createTimeout = (func: any, time: any) => {
  if (typeof func !== 'function') {
    func = () => {};
  }
  if (typeof time !== 'number') time = 0;

  const maxSec = 2147472000; // maximum 24.855 days
  return setTimeout(func, time > maxSec ? maxSec : time);
};

// Crypto utilities
export const encodeSignature = (data: any, secretKey: string): string => {
  try {
    const signature = CryptoJS.HmacSHA256(JSON.stringify(data), secretKey).toString();
    return signature;
  } catch (e) {
    console.error('Failed to encode signature', e);
    return '';
  }
};

export const decodeSignature = (data: any): any => {
  try {
    if (!data?.value) return {};
    return JSON.parse(data.value);
  } catch (e) {
    console.error('Failed to decode signature', e);
    return {};
  }
};

// App utilities
export const openAppMobile = (url: string): void => {
  if (typeof window !== 'undefined' && url) {
    window.location.href = url;
  }
};

// Screen utilities
export const checkIsFullscreen = (): boolean => {
  if (typeof document === 'undefined') return false;

  return !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  );
};

// Content parsing utilities
export const parseParamsFromContent = (content: any): any => {
  if (!content) return {};

  return {
    id: content.id,
    title: content.title,
    type: content.type,
    slug: content.slug || content.seo?.slug
  };
};

// Video progress utilities
export const parseRemainText = (progress: number, duration: number) => {
  if (!duration || duration <= 0) {
    return {
      progressPercent: 0,
      remainText: ''
    };
  }

  let progressPercent = Math.round((progress / duration) * 100);
  const remainTime = duration - progress;
  const remainMinutes = Math.floor(remainTime / 60);
  const remainSeconds = Math.floor(remainTime % 60);

  let remainText = '';
  if (remainMinutes > 0) {
    remainText = `${remainMinutes} phút`;
    if (remainSeconds > 0) {
      remainText += ` ${remainSeconds} giây`;
    }
  } else if (remainSeconds > 0) {
    remainText = `${remainSeconds} giây`;
  }

  if (progress === 0) progressPercent = 0;
  if (progressPercent > 99) progressPercent = 99;

  return {
    progressPercent,
    remainText
  };
};

// URL slug utilities
export const getSLugTVodFromRouter = (url: any) => {
  const parseSplitted = url.split('--live-');
  const query = { slug: parseSplitted[0] };
  return query;
};

// UTM parameters utilities
export const UtmParams = (query: any) => {
  const utmParams: any = {};

  if (query?.utm_source) utmParams.utm_source = query.utm_source;
  if (query?.utm_medium) utmParams.utm_medium = query.utm_medium;
  if (query?.utm_campaign) utmParams.utm_campaign = query.utm_campaign;
  if (query?.utm_term) utmParams.utm_term = query.utm_term;
  if (query?.utm_content) utmParams.utm_content = query.utm_content;
  if (query?.fbclid) utmParams.fbclid = query.fbclid;
  if (query?.pid) utmParams.pid = query.pid;
  if (query?.af_adset) utmParams.af_adset = query.af_adset;
  if (query?.af_ad) utmParams.af_ad = query.af_ad;
  if (query?.c) utmParams.c = query.c;

  return utmParams;
};

// JSON utilities
export const json_to_query_string = (json: any): string => {
  return Object.keys(json)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(json[key])}`)
    .join('&');
};

// URL parameter utilities
export const setUrlParams = (url: string, params: Record<string, any>): string => {
  const urlObj = new URL(url);

  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      urlObj.searchParams.set(key, String(value));
    }
  });

  return urlObj.toString();
};

// Multi domain utilities
export const handleMultiDomainLocal = (url: string, localDomain?: string): string => {
  if (!localDomain || !url) return url;

  try {
    const urlObj = new URL(url);
    urlObj.hostname = localDomain;
    return urlObj.toString();
  } catch (e) {
    console.error('Failed to handle multi domain local', e);
    return url;
  }
};

// Token utilities
export const removeAccessTokenProfile = (): void => {
  removeLocalStorage('access_token_profile');
  removeSessionStorage('access_token_profile');
};

// Time parsing utilities
export const parseTimeStartNotify = (timestamp: any): string => {
  if (!timestamp) return '';

  const date = new Date(timestamp * 1000);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const diffDays = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return 'Hôm nay';
  } else if (diffDays === 1) {
    return 'Hôm qua';
  } else {
    return `${diffDays} ngày trước`;
  }
};

// Episode utilities
export const checkNextEpisodeToWatch = (episodes: any[], currentEpisode: any): any => {
  if (!episodes || !currentEpisode) return null;

  const currentIndex = episodes.findIndex(ep => ep.id === currentEpisode.id);
  if (currentIndex === -1 || currentIndex === episodes.length - 1) return null;

  return episodes[currentIndex + 1];
};

// Array utilities
export const parseTagsData = (tags: any[]): string[] => {
  if (!Array.isArray(tags)) return [];

  return tags
    .map(tag => {
      if (typeof tag === 'string') return tag;
      if (tag && tag.name) return tag.name;
      return '';
    })
    .filter(Boolean);
};

// Date parsing utilities
export const parteExpiredDate = (dateString: string): string => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return moment(date).format('DD/MM/YYYY');
  } catch (e) {
    console.error('Failed to parse expired date', e);
    return '';
  }
};

// Link attribute utilities
export const setLinkAttribute = (
  url: string,
  attributes: Record<string, any> = {}
): Record<string, any> => {
  const linkAttrs: Record<string, any> = {
    href: url
  };

  if (url && (url.startsWith('http') || url.startsWith('//'))) {
    linkAttrs.target = '_blank';
    linkAttrs.rel = 'noopener noreferrer';
  }

  return { ...linkAttrs, ...attributes };
};
