// Model type definitions extracted from apps/web/src/models
// This file contains all data model interfaces

export interface User {
  id: string;
  email?: string;
  mobile?: string;
  name?: string;
  givenName?: string;
  avatar?: string;
  gender?: string;
  dob?: string;
  address?: string;
  countryCode?: string;
  isPremium: boolean;
  status: string;
  createdAt?: string;
  deletedAt?: string;
  emailVerified?: boolean;
  phoneVerified?: boolean;
  allowPush?: boolean;
  allowPasswordChange?: boolean;
  passwordRequired?: boolean;
  phoneRequired?: boolean;
  forceBindPhone?: boolean;
  showGuestFlow?: boolean;
  hadTvod?: boolean;
  haveSearchHistory?: boolean;
  haveWatchlater?: boolean;
  haveWatchmore?: boolean;
  trialCode?: string;
  uuCode?: string;
  provider?: string;
  facebook?: any;
  google?: any;
  apple?: any;
  invoiceInfo?: any;
  token?: string;
  httpCode?: number;
}

export interface Profile extends User {
  // Profile extends User with additional fields if needed
}

export interface LoginResponse {
  accessToken: string;
  refreshToken?: string;
  timeExpiresInToken?: number;
  isFirstLogin: boolean;
  profile: Profile;
  message?: string;
  code?: number;
}

export interface RegisterResponse {
  accessToken?: string;
  expireAt?: string;
  isExist?: boolean;
  isFirstLogin?: boolean;
  message?: string;
  phoneNumber?: string;
  profile?: Profile;
  registerSessionId?: string;
}

export interface UserPackageInfo {
  currentTime: number;
  isSubscriptions: boolean;
  network?: string;
  subscriptions?: Subscription;
  technicalErrorMessage?: string;
}

export interface Subscription {
  id: string;
  banner?: {
    imageUrlLeft?: string;
    imageUrlRight?: string;
  };
  expiredDate?: string;
  full?: boolean;
  packageId?: string;
  packageName?: string;
  remainingDay?: number;
  status?: string;
  suggestPackageId?: string;
  suggestRibbonId?: string;
}

export interface Content {
  id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  poster?: string;
  type: number;
  contentType?: string;
  duration?: number;
  releaseYear?: number;
  avgRate?: number;
  totalRate?: number;
  views?: number;
  isPremium: boolean;
  isComingSoon?: boolean;
  isLive?: boolean;
  isBroadcasting?: boolean;
  isUpComingSoon?: boolean;
  progress?: number;
  progressPercent?: number;
  remainText?: string;
  tags?: string[];
  genres?: string[];
  categories?: string[];
  cast?: string[];
  director?: string[];
  country?: string[];
  language?: string[];
  subtitle?: string[];
  ageRange?: string;
  seo?: SEOInfo;
  images?: ContentImages;
  linkPlay?: LinkPlay;
  triggers?: any;
  metadata?: any;
  properties?: any;
  trackingData?: TrackingData;
}

export interface ContentImages {
  thumbnail?: string;
  thumbnailHot?: string;
  thumbnailBig?: string;
  thumbnailNTC?: string;
  thumbnailHotNTC?: string;
  thumbnailBigNTC?: string;
  poster?: string;
  posterNTC?: string;
  posterOriginal?: string;
  carousel?: string;
  carouselNTC?: string;
  homeVodHot?: string;
  vodThumbBig?: string;
  promotionBanner?: string;
  titleCardLight?: string;
  titleCardDark?: string;
  thumbOriginal?: string;
  imageLink?: string;
}

export interface LinkPlay {
  hlsLinkPlay?: string;
  hlsBackup1?: string;
  hlsBackup2?: string;
  dashLinkPlay?: string;
  dashBackup1?: string;
  dashBackup2?: string;
}

export interface SEOInfo {
  title?: string;
  description?: string;
  keywords?: string[];
  url?: string;
  slug?: string;
  canonical?: string;
  shareUrl?: string;
  metaRobots?: string;
  alternate?: any;
}

export interface TrackingData {
  recommendationId?: string;
  type?: string;
}

export interface Episode extends Content {
  episodeNumber?: number;
  seasonNumber?: number;
  seasonId?: string;
  seriesId?: string;
  airDate?: string;
  nextEpisode?: Episode;
  prevEpisode?: Episode;
}

export interface Season {
  id: string;
  title: string;
  name?: string;
  seasonNumber?: number;
  seriesId?: string;
  currentEpisode?: number;
  totalEpisodes?: number;
  episodes?: Episode[];
  seoUrl?: string;
}

export interface Series extends Content {
  seasons?: Season[];
  totalSeasons?: number;
  totalEpisodes?: number;
  currentSeason?: Season;
  currentEpisode?: Episode;
}

export interface LiveTV extends Content {
  channelId?: string;
  channelName?: string;
  channelNumber?: number;
  channelLogo?: string;
  epg?: EPGItem[];
  currentProgram?: EPGItem;
  nextProgram?: EPGItem;
  isLive: true;
}

export interface EPGItem {
  id: string;
  title: string;
  description?: string;
  startTime: number;
  endTime: number;
  duration: number;
  channelId: string;
  isLive?: boolean;
  progress?: number;
}

export interface LiveStream extends Content {
  streamId?: string;
  streamUrl?: string;
  chatEnabled?: boolean;
  viewerCount?: number;
  startTime?: number;
  endTime?: number;
  isLive: true;
}

export interface Ribbon {
  id: string;
  title: string;
  type: number;
  items: Content[];
  hasMore?: boolean;
  totalItems?: number;
  url?: string;
  href?: string;
  seo?: SEOInfo;
  metadata?: any;
  properties?: any;
}

export interface Banner extends Content {
  bannerType?: string;
  position?: string;
  priority?: number;
  startDate?: string;
  endDate?: string;
  clickUrl?: string;
  impressionUrl?: string;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  status: 'unread' | 'read' | 'archived';
  createdAt: string;
  readAt?: string;
  data?: any;
  actionUrl?: string;
}

export interface Transaction {
  id: string;
  amount: number;
  currency: string;
  status: string;
  method: string;
  packageId?: string;
  packageName?: string;
  packageDuration?: string;
  displayDuration?: string;
  effectiveDate?: string;
  expiryDate?: string;
  createdAt: string;
  updatedAt?: string;
  errorCode?: string;
  errorString?: string;
  message?: string;
}

export interface PaymentMethod {
  id: string;
  name: string;
  type: string;
  icon?: string;
  enabled: boolean;
  config?: any;
}

export interface Package {
  id: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  duration: number;
  durationType: string;
  features?: string[];
  popular?: boolean;
  enabled: boolean;
  metadata?: any;
}

export interface Voucher {
  id: string;
  code: string;
  name: string;
  description?: string;
  type: string;
  value: number;
  currency?: string;
  packageType?: number;
  tvodType?: number;
  tvodDurationType?: string;
  tvodWaitingDuration?: number;
  tvodConsumingDuration?: number;
  startDate?: string;
  expiredDate?: string;
  usageLimit?: number;
  usageCount?: number;
  enabled: boolean;
  groupName?: string;
  isLifeTimeTVodType?: boolean;
  isPackageTypeTVod?: boolean;
}

export interface Comment {
  id: string;
  contentId: string;
  userId: string;
  userName?: string;
  userAvatar?: string;
  message: string;
  rating?: number;
  likes?: number;
  dislikes?: number;
  replies?: Comment[];
  createdAt: string;
  updatedAt?: string;
  status: string;
}

export interface SearchResult {
  query: string;
  totalResults: number;
  results: Content[];
  suggestions?: string[];
  filters?: SearchFilter[];
  sort?: SortOption[];
}

export interface SearchFilter {
  key: string;
  label: string;
  type: 'checkbox' | 'radio' | 'range' | 'select';
  options?: FilterOption[];
  value?: any;
}

export interface FilterOption {
  label: string;
  value: any;
  count?: number;
}

export interface SortOption {
  label: string;
  value: string;
  direction: 'asc' | 'desc';
}

export interface Artist {
  id: string;
  name: string;
  avatar?: string;
  biography?: string;
  birthDate?: string;
  nationality?: string;
  roles?: string[];
  filmography?: Content[];
  awards?: string[];
  socialLinks?: Record<string, string>;
}

export interface Genre {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  color?: string;
  parentId?: string;
  children?: Genre[];
}

export interface Category extends Genre {
  // Category extends Genre
}

export interface Tag {
  id: string;
  name: string;
  slug: string;
  count?: number;
  color?: string;
}

export interface Collection {
  id: string;
  title: string;
  description?: string;
  type: string;
  items: Content[];
  totalItems?: number;
  thumbnail?: string;
  poster?: string;
  seo?: SEOInfo;
  metadata?: any;
}

export interface Playlist extends Collection {
  userId?: string;
  isPublic?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface WatchHistory {
  id: string;
  contentId: string;
  content?: Content;
  userId: string;
  progress: number;
  duration: number;
  watchedAt: string;
  completed: boolean;
  device?: string;
  platform?: string;
}

export interface Bookmark {
  id: string;
  contentId: string;
  content?: Content;
  userId: string;
  createdAt: string;
  type: 'watchlater' | 'favorite' | 'playlist';
  playlistId?: string;
}

export interface Rating {
  id: string;
  contentId: string;
  userId: string;
  rating: number;
  review?: string;
  createdAt: string;
  updatedAt?: string;
  helpful?: number;
  reported?: boolean;
}

export interface Device {
  id: string;
  name: string;
  type: string;
  os: string;
  browser?: string;
  version?: string;
  userAgent: string;
  lastActiveAt: string;
  isActive: boolean;
  location?: string;
}

export interface Session {
  id: string;
  userId: string;
  deviceId: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt: string;
  createdAt: string;
  lastActiveAt: string;
  ipAddress?: string;
  userAgent?: string;
  isActive: boolean;
}
