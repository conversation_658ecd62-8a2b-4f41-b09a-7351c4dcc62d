// Cookie configuration utilities
// This file contains cookie-related configurations and helpers

// Cookie configuration object
export const ConfigCookie = {
  // Authentication cookies
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_ID: 'user_id',
  
  // User preferences
  LANGUAGE: 'language',
  THEME: 'theme',
  VOLUME: 'volume',
  QUALITY: 'quality',
  
  // Tracking and analytics
  VISITOR_ID: 'visitor_id',
  SESSION_ID: 'session_id',
  UTM_SOURCE: 'utm_source',
  UTM_MEDIUM: 'utm_medium',
  UTM_CAMPAIGN: 'utm_campaign',
  
  // Consent and privacy
  COOKIE_CONSENT: 'cookie_consent',
  PRIVACY_CONSENT: 'privacy_consent',
  GDPR_CONSENT: 'gdpr_consent',
  
  // A/B testing
  AB_TEST_GROUP: 'ab_test_group',
  EXPERIMENT_ID: 'experiment_id',
  
  // Device and browser
  DEVICE_ID: 'device_id',
  BROWSER_ID: 'browser_id',
  FINGERPRINT: 'fingerprint',
  
  // Payment and subscription
  PAYMENT_METHOD: 'payment_method',
  SUBSCRIPTION_STATUS: 'subscription_status',
  
  // Content preferences
  CONTENT_FILTER: 'content_filter',
  AGE_RATING: 'age_rating',
  
  // App state
  FIRST_VISIT: 'first_visit',
  LAST_VISIT: 'last_visit',
  VISIT_COUNT: 'visit_count',
  
  // Feature flags
  FEATURE_FLAGS: 'feature_flags',
  BETA_FEATURES: 'beta_features',
};

// Cookie options and configurations
export const COOKIE_OPTIONS = {
  // Default options
  DEFAULT: {
    path: '/',
    secure: true,
    sameSite: 'lax' as const,
    httpOnly: false,
  },
  
  // Secure options for sensitive data
  SECURE: {
    path: '/',
    secure: true,
    sameSite: 'strict' as const,
    httpOnly: true,
  },
  
  // Session options (expires when browser closes)
  SESSION: {
    path: '/',
    secure: true,
    sameSite: 'lax' as const,
    httpOnly: false,
  },
  
  // Long-term options (1 year)
  LONG_TERM: {
    path: '/',
    secure: true,
    sameSite: 'lax' as const,
    httpOnly: false,
    maxAge: 365 * 24 * 60 * 60, // 1 year in seconds
  },
  
  // Short-term options (1 day)
  SHORT_TERM: {
    path: '/',
    secure: true,
    sameSite: 'lax' as const,
    httpOnly: false,
    maxAge: 24 * 60 * 60, // 1 day in seconds
  },
};

// Cookie expiration times (in seconds)
export const COOKIE_EXPIRATION = {
  SESSION: 0, // Session cookie
  HOUR: 60 * 60,
  DAY: 24 * 60 * 60,
  WEEK: 7 * 24 * 60 * 60,
  MONTH: 30 * 24 * 60 * 60,
  YEAR: 365 * 24 * 60 * 60,
};

// Cookie domains configuration
export const COOKIE_DOMAINS = {
  MAIN: '.vieon.vn',
  API: '.api.vieon.vn',
  CDN: '.cdn.vieon.vn',
  STATIC: '.static.vieon.vn',
};

// Cookie utility functions
export const getCookieDomain = (): string => {
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    
    // For localhost and IP addresses, don't set domain
    if (hostname === 'localhost' || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
      return '';
    }
    
    // For vieon.vn and subdomains
    if (hostname.includes('vieon.vn')) {
      return '.vieon.vn';
    }
    
    // For other domains, use the full hostname
    return hostname;
  }
  
  return '';
};

export const buildCookieString = (
  name: string,
  value: string,
  options: {
    path?: string;
    domain?: string;
    secure?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
    httpOnly?: boolean;
    maxAge?: number;
    expires?: Date;
  } = {}
): string => {
  let cookieString = `${name}=${encodeURIComponent(value)}`;
  
  if (options.path) {
    cookieString += `; Path=${options.path}`;
  }
  
  if (options.domain) {
    cookieString += `; Domain=${options.domain}`;
  }
  
  if (options.secure) {
    cookieString += '; Secure';
  }
  
  if (options.sameSite) {
    cookieString += `; SameSite=${options.sameSite}`;
  }
  
  if (options.httpOnly) {
    cookieString += '; HttpOnly';
  }
  
  if (options.maxAge !== undefined) {
    cookieString += `; Max-Age=${options.maxAge}`;
  }
  
  if (options.expires) {
    cookieString += `; Expires=${options.expires.toUTCString()}`;
  }
  
  return cookieString;
};

export const parseCookieString = (cookieString: string): Record<string, string> => {
  const cookies: Record<string, string> = {};
  
  if (!cookieString) return cookies;
  
  cookieString.split(';').forEach(cookie => {
    const [name, ...rest] = cookie.trim().split('=');
    if (name && rest.length > 0) {
      cookies[name] = decodeURIComponent(rest.join('='));
    }
  });
  
  return cookies;
};

export const isCookieEnabled = (): boolean => {
  if (typeof document === 'undefined') return false;
  
  try {
    const testCookie = '__cookie_test__';
    document.cookie = `${testCookie}=test; path=/`;
    const enabled = document.cookie.includes(testCookie);
    
    // Clean up test cookie
    document.cookie = `${testCookie}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    
    return enabled;
  } catch (e) {
    return false;
  }
};

export const getCookieSize = (): number => {
  if (typeof document === 'undefined') return 0;
  
  return document.cookie.length;
};

export const clearAllCookies = (domain?: string): void => {
  if (typeof document === 'undefined') return;
  
  const cookies = parseCookieString(document.cookie);
  const cookieDomain = domain || getCookieDomain();
  
  Object.keys(cookies).forEach(name => {
    // Clear cookie for current path
    document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    
    // Clear cookie for domain if specified
    if (cookieDomain) {
      document.cookie = `${name}=; path=/; domain=${cookieDomain}; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    }
    
    // Clear cookie for root path
    document.cookie = `${name}=; path=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
  });
};

export const clearVieOnCookies = (): void => {
  if (typeof document === 'undefined') return;
  
  const cookies = parseCookieString(document.cookie);
  const vieonCookies = Object.keys(cookies).filter(name => 
    name.startsWith('vieon_') || 
    Object.values(ConfigCookie).includes(name)
  );
  
  const cookieDomain = getCookieDomain();
  
  vieonCookies.forEach(name => {
    document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    
    if (cookieDomain) {
      document.cookie = `${name}=; path=/; domain=${cookieDomain}; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    }
  });
};

// GDPR and privacy compliance helpers
export const hasConsentForCookie = (cookieName: string): boolean => {
  if (typeof document === 'undefined') return false;
  
  const consent = parseCookieString(document.cookie)[ConfigCookie.COOKIE_CONSENT];
  
  if (!consent) return false;
  
  try {
    const consentData = JSON.parse(consent);
    return consentData[cookieName] === true;
  } catch (e) {
    return false;
  }
};

export const setConsentForCookie = (cookieName: string, hasConsent: boolean): void => {
  if (typeof document === 'undefined') return;
  
  const existingConsent = parseCookieString(document.cookie)[ConfigCookie.COOKIE_CONSENT];
  let consentData: Record<string, boolean> = {};
  
  if (existingConsent) {
    try {
      consentData = JSON.parse(existingConsent);
    } catch (e) {
      // Invalid JSON, start fresh
    }
  }
  
  consentData[cookieName] = hasConsent;
  
  const cookieString = buildCookieString(
    ConfigCookie.COOKIE_CONSENT,
    JSON.stringify(consentData),
    COOKIE_OPTIONS.LONG_TERM
  );
  
  document.cookie = cookieString;
};

// Default export for backward compatibility
export default ConfigCookie;
