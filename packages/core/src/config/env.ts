// Environment configuration utilities extracted from apps/web/src/config
// This file contains environment-related configuration helpers

export interface EnvConfig {
  NODE_ENV: string;
  WEB_ENV: string;
  API_BASE_URL: string;
  CDN_URL: string;
  DOMAIN: string;
  APP_VERSION: string;
  BUILD_TIME: string;
  SENTRY_DSN?: string;
  GOOGLE_ANALYTICS_ID?: string;
  FACEBOOK_APP_ID?: string;
  GOOGLE_CLIENT_ID?: string;
  APPLE_CLIENT_ID?: string;
}

// Default environment configuration
const defaultConfig: EnvConfig = {
  NODE_ENV: 'development',
  WEB_ENV: 'develop',
  API_BASE_URL: 'https://api.vieon.vn',
  CDN_URL: 'https://cdn.vieon.vn',
  DOMAIN: 'https://vieon.vn',
  APP_VERSION: '1.0.0',
  BUILD_TIME: new Date().toISOString(),
};

// Get environment configuration
export const getEnvConfig = (): EnvConfig => {
  if (typeof window !== 'undefined' && (window as any).__ENV_CONFIG__) {
    return { ...defaultConfig, ...(window as any).__ENV_CONFIG__ };
  }

  if (typeof process !== 'undefined' && process.env) {
    return {
      ...defaultConfig,
      NODE_ENV: process.env.NODE_ENV || defaultConfig.NODE_ENV,
      WEB_ENV: process.env.WEB_ENV || defaultConfig.WEB_ENV,
      API_BASE_URL: process.env.API_BASE_URL || defaultConfig.API_BASE_URL,
      CDN_URL: process.env.CDN_URL || defaultConfig.CDN_URL,
      DOMAIN: process.env.DOMAIN || defaultConfig.DOMAIN,
      APP_VERSION: process.env.APP_VERSION || defaultConfig.APP_VERSION,
      BUILD_TIME: process.env.BUILD_TIME || defaultConfig.BUILD_TIME,
      SENTRY_DSN: process.env.SENTRY_DSN,
      GOOGLE_ANALYTICS_ID: process.env.GOOGLE_ANALYTICS_ID,
      FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID,
      GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
      APPLE_CLIENT_ID: process.env.APPLE_CLIENT_ID,
    };
  }

  return defaultConfig;
};

// Environment checks
export const isDevelopment = (): boolean => {
  const config = getEnvConfig();
  return config.NODE_ENV === 'development' || config.WEB_ENV === 'develop';
};

export const isProduction = (): boolean => {
  const config = getEnvConfig();
  return config.NODE_ENV === 'production' && config.WEB_ENV === 'production';
};

export const isStaging = (): boolean => {
  const config = getEnvConfig();
  return config.WEB_ENV === 'staging';
};

export const isTesting = (): boolean => {
  const config = getEnvConfig();
  return config.WEB_ENV === 'testing';
};

// API configuration
export const getApiConfig = () => {
  const config = getEnvConfig();
  
  return {
    baseURL: config.API_BASE_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    withCredentials: false,
  };
};

// CDN configuration
export const getCdnConfig = () => {
  const config = getEnvConfig();
  
  return {
    baseURL: config.CDN_URL,
    imageFormats: ['webp', 'jpg', 'png'],
    videoFormats: ['mp4', 'webm'],
    defaultQuality: 'auto',
  };
};

// Domain configuration
export const getDomainConfig = () => {
  const config = getEnvConfig();
  
  return {
    main: config.DOMAIN,
    api: config.API_BASE_URL,
    cdn: config.CDN_URL,
    app: {
      ios: 'https://apps.apple.com/vn/app/vieon/id1451273137',
      android: 'https://play.google.com/store/apps/details?id=com.vieon.tv',
    },
  };
};

// Feature flags based on environment
export const getFeatureFlags = () => {
  const config = getEnvConfig();
  
  return {
    enableAnalytics: isProduction() || isStaging(),
    enableSentry: !!config.SENTRY_DSN,
    enableDebugMode: isDevelopment(),
    enableBetaFeatures: isDevelopment() || isTesting(),
    enableServiceWorker: isProduction(),
    enablePWA: isProduction(),
  };
};

// Third-party service configuration
export const getThirdPartyConfig = () => {
  const config = getEnvConfig();
  
  return {
    sentry: {
      dsn: config.SENTRY_DSN,
      environment: config.WEB_ENV,
      release: config.APP_VERSION,
    },
    googleAnalytics: {
      trackingId: config.GOOGLE_ANALYTICS_ID,
      enabled: !!config.GOOGLE_ANALYTICS_ID && (isProduction() || isStaging()),
    },
    facebook: {
      appId: config.FACEBOOK_APP_ID,
      enabled: !!config.FACEBOOK_APP_ID,
    },
    google: {
      clientId: config.GOOGLE_CLIENT_ID,
      enabled: !!config.GOOGLE_CLIENT_ID,
    },
    apple: {
      clientId: config.APPLE_CLIENT_ID,
      enabled: !!config.APPLE_CLIENT_ID,
    },
  };
};

// Build information
export const getBuildInfo = () => {
  const config = getEnvConfig();
  
  return {
    version: config.APP_VERSION,
    buildTime: config.BUILD_TIME,
    environment: config.WEB_ENV,
    nodeEnv: config.NODE_ENV,
  };
};

// URL builders
export const buildApiUrl = (path: string): string => {
  const config = getApiConfig();
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return `${config.baseURL}/${cleanPath}`;
};

export const buildCdnUrl = (path: string): string => {
  const config = getCdnConfig();
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return `${config.baseURL}/${cleanPath}`;
};

export const buildAppUrl = (path: string): string => {
  const config = getDomainConfig();
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return `${config.main}/${cleanPath}`;
};

// Environment-specific configurations
export const getEnvironmentSpecificConfig = () => {
  const config = getEnvConfig();
  
  switch (config.WEB_ENV) {
    case 'production':
      return {
        logLevel: 'error',
        enableDebugTools: false,
        cacheTimeout: 3600000, // 1 hour
        apiTimeout: 30000,
      };
    
    case 'staging':
      return {
        logLevel: 'warn',
        enableDebugTools: true,
        cacheTimeout: 1800000, // 30 minutes
        apiTimeout: 45000,
      };
    
    case 'testing':
      return {
        logLevel: 'info',
        enableDebugTools: true,
        cacheTimeout: 300000, // 5 minutes
        apiTimeout: 60000,
      };
    
    case 'develop':
    default:
      return {
        logLevel: 'debug',
        enableDebugTools: true,
        cacheTimeout: 60000, // 1 minute
        apiTimeout: 120000,
      };
  }
};
