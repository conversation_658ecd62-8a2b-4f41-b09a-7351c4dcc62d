// Image configuration utilities
// This file contains image asset configurations

import { getCdnConfig } from './env';

// Define a type for the ConfigImage object
export type ConfigImageType = {
  [key: string]: string;
};

// Get static domain from environment or use default
const getStaticDomain = (): string => {
  if (typeof process !== 'undefined' && process.env?.NEXT_PUBLIC_STATIC_DOMAIN) {
    return process.env.NEXT_PUBLIC_STATIC_DOMAIN + '/';
  }
  
  const cdnConfig = getCdnConfig();
  return cdnConfig.baseURL + '/';
};

const STATIC_DOMAIN = getStaticDomain();

export const ConfigImage: ConfigImageType = {
  // Logo assets
  logoVieON: STATIC_DOMAIN + 'assets/img/logo.svg',
  logoRounded: STATIC_DOMAIN + 'assets/img/logo-rounded.png',
  logoVieONPng: STATIC_DOMAIN + 'assets/img/logo.png',
  logoVieONSmartBanner: STATIC_DOMAIN + 'assets/img/vieon_app_icon.png',
  logoSloganVieON: STATIC_DOMAIN + 'assets/img/logo_slogan.svg',
  logoFooter: STATIC_DOMAIN + 'assets/img/logo.svg',
  logoMit: STATIC_DOMAIN + 'assets/img/sprites/logo-mit.svg',
  iconLogo: STATIC_DOMAIN + 'assets/img/logo.svg',
  defaultLogoBrand: STATIC_DOMAIN + 'assets/img/logo.svg',
  logoVieONInQRCode: STATIC_DOMAIN + 'assets/img/sprites/logo-vieon-qr-code.png',
  
  // UI elements
  topTen: STATIC_DOMAIN + 'assets/img/sprites/top_ten.svg',
  freshChat: STATIC_DOMAIN + 'assets/images/CSKH.svg',
  iconSmartBanner: STATIC_DOMAIN + 'assets/img/icons-192_vieon.png',
  
  // Empty states
  emptyNotify: STATIC_DOMAIN + 'assets/img/states/empty-notify.png',
  emptyRibbon: STATIC_DOMAIN + 'assets/img/states/empty-genre.png',
  emptyState: STATIC_DOMAIN + 'assets/img/states/404.png',
  emptySearch: STATIC_DOMAIN + 'assets/img/states/empty-search.png',
  emptyCat: STATIC_DOMAIN + 'assets/img/empty/not-found-cat.svg',
  emptyActivityKids: STATIC_DOMAIN + 'assets/img/empty/empty-activity-kid.svg',
  emptyKid: STATIC_DOMAIN + 'assets/img/empty/empty-kid.svg',
  emptyChannel: STATIC_DOMAIN + 'assets/img/states/empty-satellite-hide.svg',
  emptyNotification: STATIC_DOMAIN + 'assets/img/states/empty_notification.png',
  emptySchedule: STATIC_DOMAIN + 'assets/img/empty-comingsoon.png',
  emptyRestriction: STATIC_DOMAIN + 'assets/img/empty/empty-restriction.svg',
  emptyContentRestriction: STATIC_DOMAIN + 'assets/img/empty/empty-content-restriction.svg',
  
  // Network and error states
  networkCat: STATIC_DOMAIN + 'assets/img/empty/network-cat.png',
  errorLoad: STATIC_DOMAIN + 'assets/img/errorload.svg',
  liveTV429: STATIC_DOMAIN + 'assets/img/states/liveTV429.png',
  liveTV405: STATIC_DOMAIN + 'assets/img/states/liveTV405.png',
  blockAccount: STATIC_DOMAIN + 'assets/img/states/blockACC.png',
  limitCCU: STATIC_DOMAIN + 'assets/images/limit_ccu.svg',
  errorADsAbove: STATIC_DOMAIN + 'assets/img/error_ads_above.svg',
  errorADsBelow: STATIC_DOMAIN + 'assets/img/error_ads_below.svg',
  
  // Default images
  defaultAvatar: STATIC_DOMAIN + 'assets/img/user-default.png',
  defaultBanner16x9: STATIC_DOMAIN + 'assets/img/posts/thumb_default_16x9.svg',
  avatarProfileDefault: STATIC_DOMAIN + 'assets/img/avatar-default.svg',
  defaultPoster: STATIC_DOMAIN + 'assets/img/posts/thumb_vertical_default.svg',
  defaultOriginal: STATIC_DOMAIN + 'assets/img/posts/thumb_vertical_default.svg',
  artistDefault: STATIC_DOMAIN + 'assets/img/artist-avatar-default.svg',
  
  // Tags and badges
  imgTagNew: STATIC_DOMAIN + 'assets/img/sprites/tag_new_green.svg',
  imgTagVip: STATIC_DOMAIN + 'assets/img/sprites/diamond_vip_pink.svg',
  vipPink: STATIC_DOMAIN + 'assets/img/sprites/diamond_vip_pink.svg',
  top10Tag: STATIC_DOMAIN + 'assets/img/sprites/top10-tag.svg',
  newTag: STATIC_DOMAIN + 'assets/img/sprites/new-tag.svg',
  
  // Icons and sprites
  starYellowGradient: STATIC_DOMAIN + 'assets/img/sprites/star_yellow_gradient.svg',
  channelBolt: STATIC_DOMAIN + 'assets/img/sprites/bolt.svg',
  commentFirst: STATIC_DOMAIN + 'assets/img/sprites/empty-comment.svg',
  clock: STATIC_DOMAIN + 'assets/img/sprites/clock.svg',
  calendar: STATIC_DOMAIN + 'assets/img/sprites/calendar.svg',
  dot: STATIC_DOMAIN + 'assets/img/dot.svg',
  dots: STATIC_DOMAIN + 'assets/img/states/dots.svg',
  remove: STATIC_DOMAIN + 'assets/img/states/delete.svg',
  tickNoti: STATIC_DOMAIN + 'assets/img/states/tick-noti.svg',
  tickTime: STATIC_DOMAIN + 'assets/img/tick_time.svg',
  tickFreeEpisode: STATIC_DOMAIN + 'assets/img/tick-free-episode.svg',
  tickVipEpisode: STATIC_DOMAIN + 'assets/img/tick-vip-episode.svg',
  tickSuccess: STATIC_DOMAIN + 'assets/img/tick-success.svg',
  closeCircle: STATIC_DOMAIN + 'assets/img/close-circle.svg',
  
  // Illustrations and patterns
  Illustration: STATIC_DOMAIN + 'assets/img/sprites/Illustration_process.svg',
  imgComingSoon: STATIC_DOMAIN + 'assets/img/sprites/livestream-image.svg',
  imgLoginTV: STATIC_DOMAIN + 'assets/img/sprites/login-tv.svg',
  imgLoginTVWithCode: STATIC_DOMAIN + 'assets/img/sprites/login-tv-code.png',
  startCircle: STATIC_DOMAIN + 'assets/img/sprites/start_circle.png',
  heartCircle: STATIC_DOMAIN + 'assets/img/sprites/heart_circle.png',
  jackBuyVip: STATIC_DOMAIN + 'assets/img/sprites/pattern_buy_vip.png',
  thuyNganLogin: STATIC_DOMAIN + 'assets/img/sprites/pattern_popup_login.png',
  voucher: STATIC_DOMAIN + 'assets/img/sprites/pattern_happy_state_human.png',
  voucherSuccess: STATIC_DOMAIN + 'assets/img/sprites/hand_tick_ok.svg',
  bindAccountBanner: STATIC_DOMAIN + 'assets/img/sprites/bind-account-bg.png',
  promoteFirstPay: STATIC_DOMAIN + 'assets/img/promo-first-pay.svg',
  
  // Banners
  bannerNonVip: STATIC_DOMAIN + 'assets/img/posts/banner_marketing_user_non_vip.jpg',
  bannerVip: STATIC_DOMAIN + 'assets/img/posts/banner_marketing_user_vip.jpg',
  bannerPreOrder: STATIC_DOMAIN + 'assets/img/posts/banner-notify-player.png',
  bannerPreOrderMobile: STATIC_DOMAIN + 'assets/img/posts/banner-notify-player-mobile.png',
  bannerVipMultiProfile: STATIC_DOMAIN + 'assets/img/banner-vip-multi-profile.svg',
  bannerVipNonLogin: 'https://static.vieon.vn/vieon-images/web_banner_no-login.png',
  bannerVipLogin: 'https://static.vieon.vn/vieon-images/web_banner_login.png',
  bannerVipLoginVip: 'https://static.vieon.vn/vieon-images/web_banner_vip.png',
  
  // Backgrounds
  channelBackdrop: STATIC_DOMAIN + 'assets/img/chanel-backdrop.png',
  channelBackdropForLarge: STATIC_DOMAIN + 'assets/img/chanel-backdrop-large.png',
  bgVieOnEndStream: STATIC_DOMAIN + 'assets/img/bg-vieon-end_stream.png',
  exshBackdrop: STATIC_DOMAIN + 'assets/images/bg_exsh.png',
  rapVietBackdrop: STATIC_DOMAIN + 'assets/images/bg_rv_desktop.png',
  
  // Payment related
  paymentSuccess: STATIC_DOMAIN + 'assets/img/hand_tick_ok.svg',
  paymentFail: STATIC_DOMAIN + 'assets/img/info_exclamation_mark.svg',
  paymentProcess: STATIC_DOMAIN + 'assets/img/info_percent.svg',
  
  // Social media icons
  linkedFacebook: STATIC_DOMAIN + 'assets/img/icon/facebook-icon.svg',
  linkedGoogle: STATIC_DOMAIN + 'assets/img/icon/google-icon.svg',
  linkedApple: STATIC_DOMAIN + 'assets/img/icon/apple-icon.svg',
  
  // Download app
  downLoadAppStore: STATIC_DOMAIN + 'assets/img/sprites/Download-Appstore.svg',
  downLoadAppGGPlay: STATIC_DOMAIN + 'assets/img/sprites/download-googleplay.svg',
  qrDownloadAppFooter: STATIC_DOMAIN + 'assets/img/sprites/qr-down-app-footer.svg',
  
  // Misc
  favicon: STATIC_DOMAIN + 'assets/img/favicon.ico',
  logoPciDss: STATIC_DOMAIN + 'assets/img/pci-dss-compliant-logo-vector.svg',
  imgSprites: STATIC_DOMAIN + 'assets/img/sprites',
  footerBillPayment: STATIC_DOMAIN + 'assets/img/sprites/footer-bill-payment.png',
  exshLogo: STATIC_DOMAIN + 'assets/img/sprites/exsh-logo.svg',
  
  // Comment states
  commentBlocked: STATIC_DOMAIN + 'assets/img/empty/comment-blocked.png',
  commentBlockedMobile: STATIC_DOMAIN + 'assets/img/empty/comment-blocked-mb.png',
};

// Helper functions for image configuration
export const getImageUrl = (key: string): string => {
  return ConfigImage[key] || '';
};

export const buildImageUrl = (path: string, domain?: string): string => {
  const baseDomain = domain || STATIC_DOMAIN;
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return `${baseDomain}${cleanPath}`;
};

export const getResponsiveImageUrl = (
  basePath: string, 
  sizes: string[] = ['small', 'medium', 'large']
): Record<string, string> => {
  const urls: Record<string, string> = {};
  
  sizes.forEach(size => {
    const path = basePath.replace(/(\.[^.]+)$/, `_${size}$1`);
    urls[size] = buildImageUrl(path);
  });
  
  return urls;
};

// Image optimization helpers
export const optimizeImageUrl = (
  url: string, 
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpg' | 'png';
  } = {}
): string => {
  if (!url) return '';
  
  const params = new URLSearchParams();
  
  if (options.width) params.set('w', options.width.toString());
  if (options.height) params.set('h', options.height.toString());
  if (options.quality) params.set('q', options.quality.toString());
  if (options.format) params.set('f', options.format);
  
  const queryString = params.toString();
  return queryString ? `${url}?${queryString}` : url;
};

// Default export for backward compatibility
export default ConfigImage;
