// Storage configuration utilities extracted from apps/web/src/config
// This file contains storage-related configuration and constants

// LocalStorage keys
export const LOCAL_STORAGE_KEYS = {
  // Authentication
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  ACCESS_TOKEN_PROFILE: 'access_token_profile',
  USER_PROFILE: 'user_profile',
  LOGIN_TYPE: 'login_type',
  REMEMBER_LOGIN: 'remember_login',
  
  // User preferences
  LANGUAGE: 'language',
  THEME: 'theme',
  VOLUME: 'volume',
  QUALITY: 'quality',
  SUBTITLE: 'subtitle',
  AUTOPLAY: 'autoplay',
  
  // Player settings
  PLAYER_VOLUME: 'player_volume',
  PLAYER_QUALITY: 'player_quality',
  PLAYER_SUBTITLE: 'player_subtitle',
  PLAYER_SPEED: 'player_speed',
  PLAYER_THEATER_MODE: 'player_theater_mode',
  
  // Content preferences
  CONTINUE_WATCHING: 'continue_watching',
  WATCH_HISTORY: 'watch_history',
  BOOKMARKS: 'bookmarks',
  FAVORITES: 'favorites',
  WATCH_LATER: 'watch_later',
  
  // Search and filters
  SEARCH_HISTORY: 'search_history',
  RECENT_SEARCHES: 'recent_searches',
  FILTER_PREFERENCES: 'filter_preferences',
  
  // App state
  FIRST_VISIT: 'first_visit',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  LAST_ACTIVE: 'last_active',
  APP_VERSION: 'app_version',
  
  // Device and session
  DEVICE_ID: 'device_id',
  SESSION_ID: 'session_id',
  DEVICE_INFO: 'device_info',
  
  // Analytics and tracking
  UTM_PARAMS: 'utm_params',
  REFERRER: 'referrer',
  TRACKING_ID: 'tracking_id',
  
  // Payment and subscription
  PAYMENT_METHOD: 'payment_method',
  SUBSCRIPTION_INFO: 'subscription_info',
  PACKAGE_INFO: 'package_info',
  
  // Notifications
  NOTIFICATION_SETTINGS: 'notification_settings',
  PUSH_TOKEN: 'push_token',
  
  // Cache
  API_CACHE: 'api_cache',
  CONTENT_CACHE: 'content_cache',
  IMAGE_CACHE: 'image_cache',
  
  // Debug and development
  DEBUG_MODE: 'debug_mode',
  LOG_LEVEL: 'log_level',
  FEATURE_FLAGS: 'feature_flags',
};

// SessionStorage keys
export const SESSION_STORAGE_KEYS = {
  // Temporary authentication
  TEMP_ACCESS_TOKEN: 'temp_access_token',
  TEMP_USER_DATA: 'temp_user_data',
  
  // Form data
  FORM_DATA: 'form_data',
  REGISTRATION_DATA: 'registration_data',
  PAYMENT_DATA: 'payment_data',
  
  // Navigation state
  PREVIOUS_ROUTE: 'previous_route',
  SCROLL_POSITION: 'scroll_position',
  TAB_STATE: 'tab_state',
  
  // Player state
  PLAYER_STATE: 'player_state',
  CURRENT_CONTENT: 'current_content',
  PLAYBACK_POSITION: 'playback_position',
  
  // Search state
  SEARCH_RESULTS: 'search_results',
  SEARCH_FILTERS: 'search_filters',
  
  // Modal and popup state
  MODAL_STATE: 'modal_state',
  POPUP_STATE: 'popup_state',
  
  // Temporary cache
  TEMP_CACHE: 'temp_cache',
  PAGE_CACHE: 'page_cache',
};

// Cookie keys
export const COOKIE_KEYS = {
  // Authentication
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  
  // User preferences
  LANGUAGE: 'lang',
  THEME: 'theme',
  
  // Tracking and analytics
  VISITOR_ID: 'visitor_id',
  SESSION_ID: 'session_id',
  UTM_SOURCE: 'utm_source',
  
  // Consent and privacy
  COOKIE_CONSENT: 'cookie_consent',
  PRIVACY_CONSENT: 'privacy_consent',
  
  // A/B testing
  AB_TEST_GROUP: 'ab_test_group',
  EXPERIMENT_ID: 'experiment_id',
};

// Storage configuration
export const STORAGE_CONFIG = {
  // Expiration times (in milliseconds)
  EXPIRATION: {
    SHORT: 5 * 60 * 1000, // 5 minutes
    MEDIUM: 30 * 60 * 1000, // 30 minutes
    LONG: 24 * 60 * 60 * 1000, // 24 hours
    WEEK: 7 * 24 * 60 * 60 * 1000, // 1 week
    MONTH: 30 * 24 * 60 * 60 * 1000, // 30 days
    YEAR: 365 * 24 * 60 * 60 * 1000, // 1 year
  },
  
  // Size limits (in bytes)
  SIZE_LIMITS: {
    LOCAL_STORAGE: 5 * 1024 * 1024, // 5MB
    SESSION_STORAGE: 5 * 1024 * 1024, // 5MB
    COOKIE: 4 * 1024, // 4KB
    INDEXED_DB: 50 * 1024 * 1024, // 50MB
  },
  
  // Cleanup intervals
  CLEANUP_INTERVALS: {
    CACHE: 60 * 60 * 1000, // 1 hour
    EXPIRED_ITEMS: 24 * 60 * 60 * 1000, // 24 hours
    OLD_LOGS: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
};

// Storage utility functions
export const createStorageKey = (prefix: string, key: string): string => {
  return `vieon_${prefix}_${key}`;
};

export const isStorageAvailable = (type: 'localStorage' | 'sessionStorage'): boolean => {
  if (typeof window === 'undefined') return false;
  
  try {
    const storage = window[type];
    const testKey = '__storage_test__';
    storage.setItem(testKey, 'test');
    storage.removeItem(testKey);
    return true;
  } catch (e) {
    return false;
  }
};

export const getStorageSize = (type: 'localStorage' | 'sessionStorage'): number => {
  if (!isStorageAvailable(type)) return 0;
  
  const storage = window[type];
  let totalSize = 0;
  
  for (let key in storage) {
    if (storage.hasOwnProperty(key)) {
      totalSize += storage[key].length + key.length;
    }
  }
  
  return totalSize;
};

export const clearExpiredItems = (type: 'localStorage' | 'sessionStorage'): void => {
  if (!isStorageAvailable(type)) return;
  
  const storage = window[type];
  const now = Date.now();
  const keysToRemove: string[] = [];
  
  for (let key in storage) {
    if (storage.hasOwnProperty(key) && key.startsWith('vieon_')) {
      try {
        const item = JSON.parse(storage[key]);
        if (item.expiry && now > item.expiry) {
          keysToRemove.push(key);
        }
      } catch (e) {
        // Invalid JSON, remove the item
        keysToRemove.push(key);
      }
    }
  }
  
  keysToRemove.forEach(key => storage.removeItem(key));
};

export const getStorageQuota = async (): Promise<{ usage: number; quota: number } | null> => {
  if ('storage' in navigator && 'estimate' in navigator.storage) {
    try {
      const estimate = await navigator.storage.estimate();
      return {
        usage: estimate.usage || 0,
        quota: estimate.quota || 0,
      };
    } catch (e) {
      console.warn('Failed to get storage quota:', e);
    }
  }
  return null;
};

// Storage event handlers
export const onStorageChange = (callback: (event: StorageEvent) => void): (() => void) => {
  if (typeof window === 'undefined') return () => {};
  
  window.addEventListener('storage', callback);
  
  return () => {
    window.removeEventListener('storage', callback);
  };
};

// Storage cleanup utilities
export const cleanupStorage = (): void => {
  clearExpiredItems('localStorage');
  clearExpiredItems('sessionStorage');
};

export const clearAllVieOnStorage = (): void => {
  if (!isStorageAvailable('localStorage')) return;
  
  const localStorage = window.localStorage;
  const keysToRemove: string[] = [];
  
  for (let key in localStorage) {
    if (localStorage.hasOwnProperty(key) && key.startsWith('vieon_')) {
      keysToRemove.push(key);
    }
  }
  
  keysToRemove.forEach(key => localStorage.removeItem(key));
  
  if (isStorageAvailable('sessionStorage')) {
    const sessionStorage = window.sessionStorage;
    const sessionKeysToRemove: string[] = [];
    
    for (let key in sessionStorage) {
      if (sessionStorage.hasOwnProperty(key) && key.startsWith('vieon_')) {
        sessionKeysToRemove.push(key);
      }
    }
    
    sessionKeysToRemove.forEach(key => sessionStorage.removeItem(key));
  }
};

// Migration utilities for storage schema changes
export const migrateStorageSchema = (version: string): void => {
  const currentVersion = localStorage.getItem(LOCAL_STORAGE_KEYS.APP_VERSION);
  
  if (currentVersion !== version) {
    // Perform migration based on version differences
    console.log(`Migrating storage from ${currentVersion} to ${version}`);
    
    // Example migration logic
    if (!currentVersion || currentVersion < '2.0.0') {
      // Migrate old keys to new format
      const oldKeys = ['old_key_1', 'old_key_2'];
      oldKeys.forEach(oldKey => {
        const value = localStorage.getItem(oldKey);
        if (value) {
          localStorage.setItem(createStorageKey('migrated', oldKey), value);
          localStorage.removeItem(oldKey);
        }
      });
    }
    
    localStorage.setItem(LOCAL_STORAGE_KEYS.APP_VERSION, version);
  }
};
