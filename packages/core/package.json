{"name": "@vieon/core", "version": "1.0.0", "description": "Core utilities, constants, helpers, and types for VieON applications", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup && tsc --emitDeclarationOnly", "build:js": "tsup", "dev": "tsup --watch", "clean": "rimraf dist node_modules/.cache", "typecheck": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"lodash": "^4.17.21", "date-fns": "^4.1.0", "crypto-js": "^4.2.0", "moment": "^2.29.4", "bowser": "^2.11.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.17", "@types/react": "^18.0.0", "tsup": "^8.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": ">=17.0.0"}, "publishConfig": {"access": "restricted"}}