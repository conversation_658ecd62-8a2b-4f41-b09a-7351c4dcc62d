import PaymentV2Api from '@apis/PaymentV2';
import TrackingPayment from '@tracking/functions/payment';
import { PAGE, PAYMENT_METHOD, PAYMENT_METHOD_BE } from '@constants/constants';
import { DOMAIN_WEB } from '@config/ConfigEnv';
import { TEXT } from '@constants/text';
import { createTransactionSuccess } from './payment';
import { setLoading, setToast } from '@actions/app';
import { ACTION_TYPE, createAction } from './actionType';

const trackingPayment = new TrackingPayment();

const createTransaction =
  ({
    selectedTerm,
    selectedMethod,
    promotionData,
    valueReferralCode,
    tokenId,
    isSegmentedUser
  }: any) =>
  (dispatch: any) => {
    dispatch(setLoading(true));
    return PaymentV2Api.createTransaction({
      paymentMethod: PAYMENT_METHOD_BE.WALLET,
      paymentService: PAYMENT_METHOD.MOMO,
      packageId: selectedTerm?.id,
      promotionCode: promotionData?.promotionCode || selectedTerm?.giftCode || '',
      tokenId
    })?.then((res: any) => {
      dispatch(setLoading(false));
      const result = res?.data?.result || {};
      trackingPayment.payButtonSelected({
        selectedTerm,
        promotionData,
        selectedMethod,
        transaction: result,
        isSegmentedUser
      });
      if (!res?.success) {
        dispatch(
          createAction(
            ACTION_TYPE.SET_TOAST,
            {
              message: res?.data?.error_message || TEXT.MSG_ERROR
            },
            dispatch
          )
        );
      } else if (res?.data?.error_code === 1) {
        dispatch(
          createAction(
            ACTION_TYPE.SET_TOAST,
            {
              message: res?.data?.error_message || TEXT.MSG_ERROR
            },
            dispatch
          )
        );
      } else {
        dispatch(createTransactionSuccess({ data: result, valueReferralCode }));
        dispatch(
          getInfoTransaction({
            orderId: result?.order_id,
            returnUrl: `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}?method=momo`
          })
        );
      }
      return result;
    });
  };
const getInfoTransaction =
  ({ orderId, returnUrl }: any) =>
  (dispatch: any) => {
    dispatch(setLoading(true));
    return PaymentV2Api.getInfoTransaction({ orderId, returnUrl })?.then((res: any) => {
      dispatch(setLoading(false));
      const transactionMomo = res?.data?.result || {};
      const redirectUrl = transactionMomo?.data?.redirect_url;
      if (!res?.success) {
        if (res?.httpCode === 404) {
          dispatch(setToast({ message: res?.data?.error_message || TEXT.TRANSACTION_NOT_FOUND }));
        } else {
          dispatch(setToast({ message: res?.data?.error_message || TEXT.MSG_ERROR }));
        }
      } else if (redirectUrl) {
        setTimeout(() => {
          window.location.href = redirectUrl;
        }, 1500);
      }
      dispatch(
        createAction(ACTION_TYPE.GET_INFO_TRANSACTION, { ...res?.data, isSuccess: res?.success })
      );
    });
  };

const getStatusTransaction =
  ({ orderId }: any) =>
  (dispatch: any) => {
    PaymentV2Api.getStatusTransaction({ orderId })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.SET_TRANSACTION_RESULT, res))
    );
  };

export { createTransaction, getStatusTransaction, getInfoTransaction };
