import PaymentV2Api from '@apis/PaymentV2';
import { PAGE, PAYMENT_METHOD, POPUP } from '@constants/constants';
import { TEXT } from '@constants/text';
import { createTransactionSuccess } from '@actions/payment';
import { DOMAIN_WEB } from '@config/ConfigEnv';
import TrackingPayment from '@tracking/functions/payment';
import { setLoading, setToast } from '@actions/app';
import { openPopup } from '@actions/popup';
import PaymentApi from '@apis/Payment';
import { isMobile } from 'react-device-detect';
import { ACTION_TYPE, createAction } from './actionType';

const trackingPayment = new TrackingPayment();

const handleTVod =
  ({
    transactionData,
    totalAmount,
    selectedPackage,
    selectedTerm,
    isRentalContent,
    isPvodContent,
    queryString,
    router
  }: any) =>
  (dispatch: any) => {
    if (!transactionData?.success) {
      dispatch(setToast({ message: transactionData?.message || TEXT.MSG_ERROR }));
    } else {
      const redirectUrl = transactionData?.redirectUrl;
      if (redirectUrl && isMobile) {
        if (redirectUrl) {
          setTimeout(() => {
            window.location.href = redirectUrl;
          }, 1500);
        }
      } else {
        dispatch(
          openPopup({
            name: POPUP.NAME.QR_SHOPEE_PAY,
            qrCodeImg: transactionData?.qrCodeImg,
            orderId: transactionData?.orderId,
            qrDisplayTime: transactionData?.qrDisplayTime,
            shopeePayTransaction: transactionData,
            selectedPackage,
            selectedTerm,
            cancelTransaction: () => cancelTransaction(dispatch),
            countDoneAction: () =>
              countDoneAction({
                transaction: transactionData,
                dispatch,
                queryString,
                router
              }),
            totalAmount,
            handle5Sec: () =>
              handleShopeePayTransaction({
                transaction: transactionData,
                isRentalContent,
                isPvodContent,
                queryString,
                router,
                dispatch
              })
          })
        );
      }
    }
    return transactionData;
  };

const handleRecurring =
  ({
    recurring,
    selectedTerm,
    selectedPackage,
    totalAmount,
    router,
    selectedMethod,
    queryString,
    promotionData,
    isRentalContent,
    isPvodContent,
    valueReferralCode
  }: any) =>
  (dispatch: any) =>
    PaymentV2Api.getListTokensSaved({
      paymentMethod: 'WALLET',
      paymentService: PAYMENT_METHOD.SHOPEE_PAY
    })?.then(async (res: any) => {
      const notTrackAccessTrade = true; // BE will check this case in charge-by-token
      const tokenId = res?.data?.result?.tokens?.[0]?.id;
      if (tokenId) {
        dispatch(setLoading(true));
        const resultTransaction = await dispatch(
          createTransaction({
            tokenId,
            notTrackAccessTrade,
            recurring,
            selectedTerm,
            selectedPackage,
            totalAmount,
            router,
            selectedMethod,
            queryString,
            promotionData,
            isRentalContent,
            isPvodContent,
            valueReferralCode
          })
        );
        if (
          isMobile &&
          (resultTransaction?.error_code === 10 || !!resultTransaction?.error_message)
        ) {
          router.push(
            `${PAGE.WALLET_LINK}?pkg=${selectedPackage?.id}&packageId=${selectedTerm?.id}&promotionCode=${promotionData?.promotionCode}&recurring=${recurring}&merchantId=VIEON`
          );
        }
      } else {
        PaymentApi.getLinkShopeePayTransaction({
          paymentMethod: 'WALLET',
          paymentService: PAYMENT_METHOD.SHOPEE_PAY,
          returnUrl: isMobile
            ? `${DOMAIN_WEB}${PAGE.WALLET_LINK}?pkg=${selectedPackage?.id}&packageId=${selectedTerm?.id}&promotionCode=${promotionData?.promotionCode}&recurring=${recurring}&merchantId=VIEON`
            : `${DOMAIN_WEB}${PAGE.HOME}`
        })?.then((res: any) => {
          const shopeePayTransaction = res?.data;
          if (res?.success) {
            const redirectUrl = shopeePayTransaction?.result?.redirect_url;
            if (redirectUrl && isMobile) {
              if (redirectUrl) {
                setTimeout(() => {
                  window.location.href = redirectUrl;
                }, 1500);
              }
            } else {
              dispatch(
                openPopup({
                  name: POPUP.NAME.QR_SHOPEE_PAY,
                  qrCodeImg: shopeePayTransaction?.result?.qr_code_img,
                  orderId: shopeePayTransaction?.orderId,
                  qrDisplayTime: shopeePayTransaction?.qrDisplayTime || '',
                  spPayTransaction: shopeePayTransaction,
                  selectedPackage,
                  selectedTerm,
                  recurring,
                  cancelTransaction: () => cancelTransaction(dispatch),
                  countDoneAction: () =>
                    countDoneAction({
                      transaction: shopeePayTransaction,
                      recurring,
                      queryString,
                      router,
                      dispatch
                    }),
                  totalAmount,
                  handle5Sec: () =>
                    handleShopeePayTransaction({
                      transaction: shopeePayTransaction,
                      isRentalContent,
                      isPvodContent,
                      recurring,
                      queryString,
                      router,
                      dispatch,
                      selectedTerm,
                      promotionData
                    })
                })
              );
            }
          } else {
            dispatch(setToast({ message: res?.data?.error_message || TEXT.MSG_ERROR }));
          }
        });
      }
    });

const createTransaction =
  ({
    tokenId,
    recurring,
    selectedTerm,
    selectedPackage,
    totalAmount,
    router,
    selectedMethod,
    queryString,
    promotionData,
    notTrackAccessTrade,
    isRentalContent,
    isPvodContent,
    packageId,
    promotionCode,
    valueReferralCode,
    isSegmentedUser
  }: any) =>
  (dispatch: any) =>
    PaymentV2Api.createTransaction({
      paymentMethod: 'WALLET',
      paymentService: PAYMENT_METHOD.SHOPEE_PAY,
      packageId: selectedTerm?.id || packageId,
      promotionCode: promotionData?.promotionCode || promotionCode || selectedTerm?.giftCode || '',
      tokenId
    })?.then((res: any) => {
      dispatch(setLoading(false));
      const result = res?.data?.result || {};
      trackingPayment.payButtonSelected({
        selectedTerm,
        promotionData,
        selectedMethod,
        transaction: result,
        isSegmentedUser
      });
      if (!res?.success) {
        if (!isMobile) {
          dispatch(setToast({ message: res?.data?.error_message || TEXT.MSG_ERROR }));
        }
      } else if (res?.data?.error_code === 1) {
        dispatch(setToast({ message: res?.data?.error_message || TEXT.MSG_ERROR }));
      } else {
        dispatch(
          createTransactionSuccess({
            data: result,
            notTrackAccessTrade,
            valueReferralCode
          })
        );
        dispatch(
          getInfoTransaction({
            orderId: result?.order_id,
            returnUrl: `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}${queryString}&orderId=${result?.order_id}&merchantId=VIEON`,
            tokenId,
            recurring,
            selectedPackage,
            selectedTerm,
            totalAmount,
            isRentalContent,
            isPvodContent,
            queryString,
            router,
            promotionData,
            promotionCode
          })
        );
      }
      return res?.data || result;
    });

const getInfoTransaction =
  ({
    orderId,
    returnUrl,
    tokenId,
    recurring,
    selectedPackage,
    selectedTerm,
    totalAmount,
    isRentalContent,
    isPvodContent,
    queryString,
    router,
    promotionData,
    promotionCode
  }: any) =>
  (dispatch: any) => {
    dispatch(setLoading(true));
    return PaymentV2Api.getInfoTransaction({
      orderId,
      returnUrl
    })?.then((res: any) => {
      dispatch(setLoading(false));
      const transactionShopeePay = res?.data?.result || {};
      if (!res?.success) {
        if (res?.httpCode === 404) {
          dispatch(setToast({ message: TEXT.TRANSACTION_NOT_FOUND }));
        } else {
          dispatch(setToast({ message: TEXT.MSG_ERROR }));
        }
      } else {
        const redirectUrl = transactionShopeePay?.data?.redirect_url;
        if (recurring && tokenId) {
          dispatch(
            gotoResultPageShopeePay({
              orderId,
              queryString,
              recurring,
              router
            })
          );
        } else if (redirectUrl && isMobile) {
          if (redirectUrl) {
            setTimeout(() => {
              window.location.href = redirectUrl;
            }, 1500);
          }
        } else {
          dispatch(
            openPopup({
              name: POPUP.NAME.QR_SHOPEE_PAY,
              qrCodeImg: transactionShopeePay?.data?.qr_code_img,
              orderId: transactionShopeePay?.orderId,
              qrDisplayTime: transactionShopeePay?.qrDisplayTime || '',
              spPayTransaction: transactionShopeePay,
              selectedPackage,
              selectedTerm,
              cancelTransaction: () => cancelTransaction(dispatch),
              countDoneAction: () =>
                countDoneAction({
                  transaction: transactionShopeePay,
                  recurring,
                  queryString,
                  router,
                  dispatch
                }),
              totalAmount,
              handle5Sec: () =>
                handleShopeePayTransaction({
                  transaction: transactionShopeePay,
                  isRentalContent,
                  isPvodContent,
                  recurring,
                  queryString,
                  router,
                  dispatch,
                  promotionData,
                  promotionCode
                })
            })
          );
        }
      }
      dispatch(
        createAction(ACTION_TYPE.GET_INFO_TRANSACTION, {
          ...res?.data,
          isSuccess: res?.success
        })
      );
    });
  };

const cancelTransaction = (dispatch: any) => {
  dispatch(openPopup());
};

const countDoneAction = ({ transaction, recurring, queryString, router, dispatch }: any) => {
  dispatch(
    gotoResultPageShopeePay({
      transaction,
      recurring,
      queryString,
      router,
      dispatch
    })
  );
  dispatch(openPopup());
};

const handleShopeePayTransaction = async ({
  transaction,
  isRentalContent,
  isPvodContent,
  recurring,
  queryString,
  router,
  dispatch,
  selectedTerm,
  promotionData
}: any) => {
  if (isRentalContent) {
    PaymentApi.tvodCheckTransaction({ orderId: transaction?.orderId })?.then((res: any) => {
      const status = res?.status;
      if (status !== 0) {
        cancelTransaction(dispatch);
        dispatch(
          gotoResultPageShopeePay({
            transaction,
            queryString,
            router
          })
        );
      }
    });
  } else if (isPvodContent) {
    PaymentApi.pvodCheckTransaction({ orderId: transaction?.orderId })?.then((res: any) => {
      const status = res?.status;
      if (status !== 0) {
        cancelTransaction(dispatch);
        dispatch(
          gotoResultPageShopeePay({
            transaction,
            queryString,
            router
          })
        );
      }
    });
  } else if (recurring) {
    PaymentV2Api.getListTokensSaved({
      paymentMethod: 'WALLET',
      paymentService: PAYMENT_METHOD.SHOPEE_PAY
    })?.then((res: any) => {
      const tokenId = res?.data?.result?.tokens?.[0]?.id;
      if (tokenId) {
        dispatch(setLoading(true));
        cancelTransaction(dispatch);
        dispatch(
          createTransaction({
            tokenId,
            recurring,
            selectedTerm,
            router,
            queryString,
            promotionData
          })
        );
      }
    });
  } else {
    dispatch(
      getStatusTransaction({
        orderId: transaction?.order_id,
        queryString,
        recurring,
        router,
        dispatch
      })
    );
  }
};
const getStatusTransaction =
  ({ orderId, queryString, recurring, router }: any) =>
  (dispatch: any) =>
    PaymentV2Api.getStatusTransaction({ orderId })?.then((res: any) => {
      const status = res?.status;
      dispatch(createAction(ACTION_TYPE.SET_TRANSACTION_RESULT, res));
      if (status !== 0) {
        cancelTransaction(dispatch);
        dispatch(
          gotoResultPageShopeePay({
            transaction: res,
            queryString,
            recurring,
            router
          })
        );
      }
    });
const gotoResultPageShopeePay =
  ({ transaction, orderId, queryString, recurring, router }: any) =>
  (dispatch: any) => {
    dispatch(setLoading(false));
    const sppOrderId =
      transaction?.txnID ||
      transaction?.txn_ref ||
      transaction?.orderId ||
      transaction?.txnRef ||
      transaction?.order_id ||
      orderId ||
      '';
    let url = PAGE.PAYMENT_RESULT;
    url += `${queryString}&method=shopeepay`;
    if (sppOrderId) url += `&sppOrderId=${sppOrderId}`;
    if (recurring) url += '&recurring=1';
    router?.push(url);
  };

export {
  getStatusTransaction,
  createAction,
  cancelTransaction,
  countDoneAction,
  handleShopeePayTransaction,
  gotoResultPageShopeePay,
  createTransaction,
  handleRecurring,
  getInfoTransaction,
  handleTVod
};
