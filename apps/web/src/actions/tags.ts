import TagApi from '@apis/cm/TagApi';
import { ACTION_TYPE, createAction } from './actionType';

const ACTION_GET_CONTENT_TAGS = 'GET_CONTENT_TAGS';
const ACTION_GET_LIST_TAGS_FILTER = 'GET_LIST_TAGS_FILTER';

const getContentTags =
  ({
    tags,
    page,
    limit,
    sort,
    accessToken,
    profileToken,
    ssr,
    userAgent,
    ipAddress,
    isGlobal,
    origin
  }: any) =>
  (dispatch: any) =>
    TagApi.getContentTags({
      tags,
      page,
      limit,
      sort,
      accessToken,
      profileToken,
      ssr,
      userAgent,
      ipAddress,
      isGlobal,
      origin
    })?.then((res: any) => {
      const result: any = {
        type: ACTION_GET_CONTENT_TAGS,
        payload: res,
        slug: tags,
        sort,
        page
      };
      dispatch(result);
      return result;
    });

const clearTagData = () => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.CLEAR_TAGS_DATA));
};

const getListTagsFilter = () => (dispatch: any) =>
  TagApi.getListTagsFilter()?.then((res: any) => {
    const result: any = {
      type: ACTION_GET_LIST_TAGS_FILTER,
      payload: res
    };
    return dispatch(result);
  });

export {
  ACTION_GET_CONTENT_TAGS,
  ACTION_GET_LIST_TAGS_FILTER,
  getContentTags,
  getListTagsFilter,
  clearTagData
};
