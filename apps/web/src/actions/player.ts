import { createAction, ACTION_TYPE } from './actionType';
export const setStatusFullscreen = (status: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_STATUS_FULLSCREEN, status));
export const setSettingSuccess = (data: any) => createAction(ACTION_TYPE.SET_SETTING_SUCCESS, data);

export const setSetting = (data: any) => (dispatch: any) => dispatch(setSettingSuccess(data));

export const setBlockPlayer =
  (status = false) =>
  (dispatch: any) =>
    dispatch(createAction(ACTION_TYPE.SET_BLOCK_PLAYER, status));
