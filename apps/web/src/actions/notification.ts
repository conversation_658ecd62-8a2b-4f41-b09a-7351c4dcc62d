import NotificationApi from '@apis/cm/NotificationApi';
import { ACTION_TYPE, createAction } from '@actions/actionType';

export const getListNotification =
  ({ page, limit }: any) =>
  (dispatch: any) =>
    NotificationApi.getListNotification({ page, limit })?.then((res: any) => {
      return dispatch(createAction(ACTION_TYPE.ACTION_GET_LIST_NOTIFICATION, res));
    });
export const getCountNotification = () => (dispatch: any) =>
  NotificationApi.getCountNotification()?.then((res: any) => {
    return dispatch(createAction(ACTION_TYPE.ACTION_COUNT_NOTIFICATION, res));
  });

export const checkPolicyConfirmation = (id: any) => (dispatch: any) => {
  NotificationApi.checkPolicyConfirmation(id)?.then((res: any) => {
    return dispatch(createAction(ACTION_TYPE.CHECK_POLICY_CONFIRM, res));
  });
};
export const getDetailPolicyAnnounce = (id: any) => (dispatch: any) => {
  NotificationApi.getDetailPolicyAnnounce(id)?.then((res: any) => {
    return dispatch(createAction(ACTION_TYPE.GET_DETAIL_POLICY_ANNOUNCE, res?.data?.result));
  });
};
export const confirmDetailPolicyAnnounce =
  ({ entityId, notifyId, status }: any) =>
  (dispatch: any) => {
    NotificationApi.confirmDetailPolicyAnnounce({ entityId, notifyId, status })?.then(
      (res: any) => {
        if (res.data?.code === 0) dispatch(statusConfirmPolicyAnnounce(true));
        return dispatch(createAction(ACTION_TYPE.CONFIRM_DETAIL_POLICY_ANNOUNCE, res.data));
      }
    );
  };
export const statusConfirmPolicyAnnounce =
  (status = false) =>
  (dispatch: any) => {
    dispatch(createAction(ACTION_TYPE.STATUS_POLICY_CONFIRM, status));
  };
export const trackingNotification = (notifyId: any) => (dispatch: any) => {
  NotificationApi.trackingNotification(notifyId)?.then((res: any) => {
    return dispatch(createAction(ACTION_TYPE.ACTION_TRACKING_NOTIFICATION, res));
  });
};
