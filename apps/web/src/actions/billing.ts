import Billing<PERSON><PERSON> from '@apis/billing/BillingApi';

const ACTION_BILLING = 'BILLING';
const ACTION_BILLING_GET_SERVICES = 'BILLING_GET_SERVICES';
const ACTION_BILLING_CREATE_TRANSACTION = 'BILLING_CREATE_TRANSACTION';
const ACTION_BILLING_CHECK_STATUS_TRANSACTION = 'BILLING_CHECK_STATUS_TRANSACTION';
const ACTION_MOMO_CREATE_TRANSACTION = 'MOMO_CREATE_TRANSACTION';
const ACTION_MOMO_CHECK_STATUS_TRANSACTION = 'MOMO_CHECK_STATUS_TRANSACTION';
const ACTION_VNPAY_CREATE_TRANSACTION = 'VNPAY_CREATE_TRANSACTION';
const ACTION_VNPAY_CHECK_STATUS_TRANSACTION = 'VNPAY_CHECK_STATUS_TRANSACTION';
const SET_VIP_INFO = 'SET_VIP_INFO';
const getBillingPackage = () => (dispatch: any) =>
  BillingApi.getBillingPackage()
    .then((resp) => {
      const result: any = {
        type: ACTION_BILLING,
        payload: resp
      };
      return dispatch(result);
    })
    .catch((error) => error.response);
const getServicesBilling = () => (dispatch: any) =>
  BillingApi.getServicesBilling()
    .then((resp) => {
      const result: any = {
        type: ACTION_BILLING_GET_SERVICES,
        payload: resp
      };
      return dispatch(result);
    })
    .catch((error) => error.response);

const createBillingTransaction =
  ({ packageId, serviceCode, orderInfo, returnUrl }: any) =>
  (dispatch: any) =>
    BillingApi.createBillingTransaction({ packageId, serviceCode, orderInfo, returnUrl })
      .then((resp) => {
        const result: any = {
          type: ACTION_BILLING_CREATE_TRANSACTION,
          payload: resp
        };
        return dispatch(result);
      })
      .catch((error) => error.response);

const checkBillingTransaction =
  ({ orderId }: any) =>
  (dispatch: any) =>
    BillingApi.checkBillingTransaction({ orderId })
      .then((resp) => {
        const result: any = {
          type: ACTION_BILLING_CHECK_STATUS_TRANSACTION,
          payload: resp
        };
        return dispatch(result);
      })
      .catch((error) => error.response);
const getUserTransaction = ({ page, limit }: any) =>
  BillingApi.getUserTransaction({ page, limit })
    .then((resp) => resp)
    .catch((error) => error.response);
const getUserTransactionSuccess = ({ page, limit }: any) =>
  BillingApi.getUserTransactionSuccess({ page, limit })
    .then((resp) => resp)
    .catch((error) => error.response);
const createMomoTransaction =
  ({ packageId }: any) =>
  (dispatch: any) =>
    BillingApi.createMomoTransaction({ packageId })
      .then((resp) => {
        const result: any = {
          type: ACTION_MOMO_CREATE_TRANSACTION,
          payload: resp
        };
        return dispatch(result);
      })
      .catch((error) => error.response);

const checkMomoTransaction =
  ({ orderId }: any) =>
  (dispatch: any) =>
    BillingApi.checkMomoTransaction({ orderId })
      .then((resp) => {
        const result: any = {
          type: ACTION_MOMO_CHECK_STATUS_TRANSACTION,
          payload: resp
        };
        return dispatch(result);
      })
      .catch((error) => error.response);

const createVnPayTransaction =
  ({ packageId, url_return, order_info }: any) =>
  (dispatch: any) =>
    BillingApi.createVnPayTransaction({ packageId, url_return, order_info })
      .then((resp) => {
        const result: any = {
          type: ACTION_VNPAY_CREATE_TRANSACTION,
          payload: resp
        };
        return dispatch(result);
      })
      .catch((error) => error.response);

const checkVnPayTransaction =
  ({ orderId }: any) =>
  (dispatch: any) =>
    BillingApi.checkVnPayTransaction({ orderId })
      .then((resp) => {
        const result: any = {
          type: ACTION_VNPAY_CHECK_STATUS_TRANSACTION,
          payload: resp
        };
        return dispatch(result);
      })
      .catch((error) => error.response);

export {
  ACTION_BILLING,
  ACTION_BILLING_GET_SERVICES,
  ACTION_BILLING_CREATE_TRANSACTION,
  ACTION_BILLING_CHECK_STATUS_TRANSACTION,
  ACTION_MOMO_CREATE_TRANSACTION,
  ACTION_MOMO_CHECK_STATUS_TRANSACTION,
  ACTION_VNPAY_CREATE_TRANSACTION,
  ACTION_VNPAY_CHECK_STATUS_TRANSACTION,
  SET_VIP_INFO,
  getBillingPackage,
  getServicesBilling,
  createBillingTransaction,
  checkBillingTransaction,
  createMomoTransaction,
  checkMomoTransaction,
  getUserTransaction,
  getUserTransactionSuccess,
  createVnPayTransaction,
  checkVnPayTransaction
};
