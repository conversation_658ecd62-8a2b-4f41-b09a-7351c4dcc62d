import PageApi from '@apis/cm/PageApi';
import ContentApi from '@apis/cm/ContentApi';
import { TEXT } from '@constants/text';
import { RIBBON_TYPE } from '@constants/constants';
import { ACTION_TYPE, createAction } from './actionType';

const ACTION_GET_DATA_RIBBONS = 'GET_DATA_RIBBONS';
const setPageBannerSuccess: any = (data: any) => createAction(ACTION_TYPE.SET_PAGE_BANNER, data);
const setPageRibbonSuccess: any = (data: any) => createAction(ACTION_TYPE.SET_PAGE_RIBBON, data);
const setRibbonDataSuccess: any = (data: any) =>
  createAction(ACTION_TYPE.SET_RIBBON_DATA, {
    ...data,
    loadedData: true,
    ribbonSlug: data?.ribbonSlug || data?.seo?.url
  });
const setDataLivestreamEventsSuccess = (data: any) =>
  createAction(ACTION_TYPE.GET_LIVESTREAM_EVENTS, data);
const getTipDataSuccess = (data: any) => createAction(ACTION_TYPE.GET_TIP_DATA_SUCCESS, data);
const addMyListSuccess = (data: any) => createAction(ACTION_TYPE.ADD_MY_LIST, data);

const getPageBanners =
  ({
    pageSlug,
    accessToken,
    profileToken,
    isMobile,
    ssr,
    ipAddress,
    userAgent,
    isGlobal,
    origin
  }: any) =>
  (dispatch: any) =>
    PageApi.getPageBanners({
      pageSlug,
      accessToken,
      profileToken,
      isMobile,
      ssr,
      ipAddress,
      userAgent,
      isGlobal,
      origin
    })?.then((res: any) => dispatch(setPageBannerSuccess({ ...res, pageSlug })));

const getPageRibbons: any =
  ({
    pageSlug,
    page,
    limit,
    accessToken,
    profileToken,
    isMobile,
    ssr,
    ipAddress,
    isSettingRankingBoard,
    isMWebToApp,
    imageMWebToApp,
    userAgent,
    isGlobal,
    origin
  }: any) =>
  (dispatch: any) =>
    PageApi.getPageRibbons({
      pageSlug,
      page,
      limit,
      accessToken,
      profileToken,
      isMobile,
      ssr,
      ipAddress,
      userAgent,
      isGlobal,
      origin
    })?.then((res: any) => {
      const ribbonSlug = res?.data?.[0]?.seo?.url;
      const firstRibbonId = res?.data?.[0]?.id;
      const firstRibbonName = res?.data?.[0]?.id;
      const firstRibbonType = res?.data?.[0]?.type;
      if (
        firstRibbonId &&
        !ssr &&
        firstRibbonType !== RIBBON_TYPE.BANNER_RIBBON_ADS &&
        firstRibbonType !== RIBBON_TYPE.PROMOTED_RIBBON_ADS
      ) {
        const limitRibBanner =
          firstRibbonType === RIBBON_TYPE.PROMOTION_BANNER ||
          firstRibbonType === RIBBON_TYPE.PROMOTION_BANNER_FUNC
            ? 8
            : 12;
        PageApi.getDataRibbonsId({
          id: firstRibbonId,
          accessToken,
          profileToken,
          ribbonName: firstRibbonName,
          ribbonOrder: 0,
          limit: limitRibBanner || limit,
          ssr,
          ipAddress,
          isMWebToApp,
          imageMWebToApp,
          isMobile,
          userAgent,
          isGlobal,
          origin
        })?.then((res: any) => {
          dispatch(setRibbonDataSuccess({ ...res?.data, ribbonSlug }));
        });
      }
      if (isSettingRankingBoard) {
        dispatch(createAction(ACTION_TYPE.SET_RANKING_BOARD, res?.isRankingBoard));
      }
      return dispatch(setPageRibbonSuccess({ ...res, pageSlug }));
    });

const getPageRibbonsID =
  ({ id, page, limit, accessToken, isTV, ssr }: any) =>
  (dispatch: any) =>
    PageApi.getPageRibbonsID({ id, page, limit, accessToken, isTV, ssr })?.then((res: any) =>
      dispatch(setPageRibbonSuccess({ ...res, id }))
    );

const getDataRibbons =
  ({
    ribbonSlug,
    page = 0,
    limit = 41,
    accessToken,
    profileToken,
    isMobile,
    ssr,
    ipAddress,
    isMWebToApp,
    imageMWebToApp,
    isGlobal,
    origin
  }: any) =>
  (dispatch: any) =>
    PageApi.getDataRibbons({
      ribbonSlug,
      page,
      limit,
      accessToken,
      profileToken,
      isMobile,
      ssr,
      ipAddress,
      isMWebToApp,
      imageMWebToApp,
      isGlobal,
      origin
    })?.then((res: any) => dispatch(setRibbonDataSuccess({ ...res?.data, ribbonSlug })));

const getDataRibbonsId: any =
  ({
    id,
    page = 0,
    limit,
    isTV,
    accessToken,
    profileToken,
    ribbonSlug,
    ribbonName,
    ribbonOrder,
    isLoadmore,
    ssr,
    ipAddress,
    isMWebToApp,
    imageMWebToApp,
    isMobile,
    userAgent,
    isGlobal,
    origin
  }: any) =>
  async (dispatch: any) => {
    const params: any = {
      id,
      page,
      isTV,
      accessToken,
      profileToken,
      ribbonOrder,
      isLoadmore,
      ribbonName,
      ssr,
      ipAddress,
      isMWebToApp,
      imageMWebToApp,
      isMobile,
      userAgent,
      isGlobal,
      origin
    };

    // Only add limit to params if it's defined
    if (limit !== undefined) {
      params.limit = limit;
    }

    const res = await PageApi.getDataRibbonsId(params);
    return dispatch(setRibbonDataSuccess({ ...res?.data, id, ribbonSlug }));
  };

const getDataLivestreamEvents =
  ({ slug, accessToken, profileToken, ssr, ipAddress, userAgent, origin }: any) =>
  (dispatch: any) =>
    PageApi.getLivestreamEvents({
      slug,
      accessToken,
      profileToken,
      ssr,
      ipAddress,
      userAgent,
      origin,
      dispatch
    })?.then((res: any) => dispatch(setDataLivestreamEventsSuccess(res)));

const getDataLivestreamEventsById =
  ({ id, accessToken }: any) =>
  (dispatch: any) =>
    PageApi.getLivestreamEventsById({ id, accessToken, dispatch })?.then((res: any) =>
      dispatch(setDataLivestreamEventsSuccess(res))
    );

const getTipData =
  ({ id }: any) =>
  (dispatch: any) =>
    ContentApi.getContentToolTips({ id })?.then((res: any) =>
      dispatch(getTipDataSuccess(res?.data))
    );

const setSubscribeComingSoon =
  ({ contentId, isSubscribe }: any) =>
  (dispatch: any) =>
    dispatch(createAction(ACTION_TYPE.SET_SUBSCRIBE_COMING_SOON, { contentId, isSubscribe }));

const setAddMyList =
  ({ contentData, isWatchLater }: any) =>
  (dispatch: any) =>
    ContentApi.addContentWatchlater({ contentId: contentData?.id })?.then((res: any) => {
      let message = TEXT.MSG_ERROR;
      if (res?.success) {
        dispatch(addMyListSuccess({ contentId: contentData?.id, isWatchLater }));
        if (isWatchLater) {
          message = TEXT.MSG_REMOVE_MY_LIST;
          dispatch(createAction(ACTION_TYPE.REMOVE_WATCH_LATER, contentData));
        } else {
          message = TEXT.MSG_ADDED_MY_LIST;
          dispatch(createAction(ACTION_TYPE.ADD_WATCH_LATER, contentData));
        }
      }
      dispatch(createAction(ACTION_TYPE.SET_TOAST, { message }, dispatch));
    });

const getSEOAllPage =
  ({
    slug,
    keyword,
    keyConfig,
    keySchema,
    keyBreadcrumbs,
    ssr,
    uncheckURL,
    ipAddress,
    userAgent,
    origin
  }: any) =>
  (dispatch: any) =>
    PageApi.getSEOAllPage({
      slug,
      keyword,
      keyConfig,
      keySchema,
      keyBreadcrumbs,
      ssr,
      uncheckURL,
      ipAddress,
      userAgent,
      origin
    })?.then((res: any) => dispatch(createAction(ACTION_TYPE.GET_SEO_ALL_PAGE, res)));

const setIsMasterBanner =
  (status = false) =>
  (dispatch: any) =>
    dispatch(createAction(ACTION_TYPE.SET_IS_MASTER_BANNER, status));
const getContentBroadcastingPending = (data: any) =>
  createAction(ACTION_TYPE.GET_CONTENT_BROADCASTING_PENDING, data);
const getContentUpcomingPending = (data: any) =>
  createAction(ACTION_TYPE.GET_CONTENT_UP_COMING_SOON_PENDING, data);

const getContentBroadcasting =
  ({ indexOfPage }: any) =>
  async (dispatch: any) => {
    dispatch(getContentBroadcastingPending(true));
    await PageApi.getContentBroadcasting({ indexOfPage })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_CONTENT_BROADCASTING, res));
      dispatch(getContentBroadcastingPending(false));
    });
  };

const getMoreContentBroadcasting =
  ({ indexOfPage }: any) =>
  async (dispatch: any) => {
    dispatch(getContentBroadcastingPending(true));
    await PageApi.getContentBroadcasting({ indexOfPage })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_CONTENT_BROADCASTING_MORE, res));
      dispatch(getContentBroadcastingPending(false));
    });
  };

const getContentUpcoming =
  ({ indexOfPage }: any) =>
  async (dispatch: any) => {
    dispatch(getContentUpcomingPending(true));
    await PageApi.getContentUpcoming({ indexOfPage })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_CONTENT_UP_COMING_SOON, res));
      dispatch(getContentUpcomingPending(false));
    });
  };

const getMoreContentUpcoming =
  ({ indexOfPage }: any) =>
  async (dispatch: any) => {
    dispatch(getContentUpcomingPending(true));
    await PageApi.getContentUpcoming({ indexOfPage })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_CONTENT_UP_COMING_SOON_MORE, res));
      dispatch(getContentUpcomingPending(false));
    });
  };
export const clearRibbonData = () => createAction(ACTION_TYPE.CLEAR_RIBBON_DATA);
export {
  ACTION_GET_DATA_RIBBONS,
  getSEOAllPage,
  getDataLivestreamEventsById,
  getDataLivestreamEvents,
  setSubscribeComingSoon,
  getPageBanners,
  setPageBannerSuccess,
  getPageRibbons,
  getPageRibbonsID,
  getDataRibbons,
  getDataRibbonsId,
  getTipData,
  setAddMyList,
  setIsMasterBanner,
  getContentBroadcasting,
  getMoreContentBroadcasting,
  getContentUpcoming,
  getMoreContentUpcoming
};
