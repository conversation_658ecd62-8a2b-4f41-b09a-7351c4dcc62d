import get from 'lodash/get';
import ContentApi from '@apis/cm/ContentApi';
import DetailApi from '@apis/detailApi';
import { HTTP_CODE, POPUP } from '@constants/constants';
import Router from 'next/router';
import PaymentApi from '@apis/Payment';
import { parseTVodFromInfo } from '@services/contentService';
import { Triggers } from '@models/subModels';
import { openPopup } from '@actions/popup';
import { setToast } from '@actions/app';
import { TEXT } from '@constants/text';
import { ACTION_TYPE, createAction } from './actionType';

declare global {
  interface Window {
    AiactivSDK: any;
  }
}

const ACTION_GET_CONTENT_DETAIL = 'GET_CONTENT_DETAIL';
const ACTION_GET_CONTENT_RELATED = 'GET_CONTENT_RELATED';
const ACTION_GET_CONTENT_RELATED_VOD = 'GET_CONTENT_RELATED_VOD';
const ACTION_GET_CONTENT_POPVER = 'GET_CONTENT_POPVER';
const ACTION_GET_CONTENT_WATCHLATER = 'GET_CONTENT_WATCHLATER';
const ACTION_GET_CONTENT_WATCHMORE = 'GET_CONTENT_WATCHMORE';
const GET_CONTENT = 'GET_CONTENT';
const GET_CONTENT_EPISODE = 'GET_CONTENT_EPISODE';
const EPISODE_LIST_DATA = 'EPISODE_LIST_DATA';
const RESET_CONTENT = 'RESET_CONTENT';
const ADD_WATCH_LATER = 'ADD_WATCH_LATER';
const REMOVE_WATCH_LATER = 'REMOVE_WATCH_LATER';
const REMOVE_WATCH_MORE = 'REMOVE_WATCH_MORE';
const ACTION_DETAIL_RIBBON_NOTFOUND = 'ACTION_DETAIL_RIBBON_NOTFOUND';
const ACTION_GET_RELATED_VOD = 'ACTION_GET_RELATED_VOD';
const ACTION_GET_RECOMMEND_VOD = 'ACTION_GET_RECOMMEND_VOD';
const ACTION_GET_EPISODE_LIST_VOD = 'ACTION_GET_EPISODE_LIST_VOD';
const ACTION_GET_CONTENT_DETAIL_ID = 'ACTION_GET_CONTENT_DETAIL_ID';
const GET_LIST_COMMENTS_SUCCESS = 'GET_LIST_COMMENTS_SUCCESS';
const GET_CONTENT_SERVICE = 'GET_CONTENT_SERVICE';
const GET_DATA_PRODUCT_BRAND = 'GET_DATA_PRODUCT_BRAND';
const SET_TYPE_ACTION_VIDEO_INDEXING = 'SET_TYPE_ACTION_VIDEO_INDEXING';
const ACTION_GET_CONTENT_BY_SLUG_SEO = 'ACTION_GET_CONTENT_BY_SLUG_SEO';
const ACTION_GET_LIST_EPISODES_CONTENT = 'ACTION_GET_LIST_EPISODES_CONTENT';

const getListCommentsSuccess = (dataResult: any) => ({
  type: GET_LIST_COMMENTS_SUCCESS,
  payload: dataResult
});

const getListComments =
  ({ contentId, page = 0, limit = 20, ipAddress, userAgent, origin }: any) =>
  (dispatch: any) => {
    if (contentId) {
      DetailApi.getCommentList({
        contentId,
        page,
        limit,
        ipAddress,
        userAgent,
        origin
      })?.then((res: any) => dispatch(getListCommentsSuccess({ ...res?.data, contentId })));
    }
  };

const getContentDetailByID =
  ({ contentId, episodeId, content, isCard, isVideoIndexingDataByContentId = false }: any) =>
  async (dispatch: any) => {
    try {
      const res = await DetailApi.getContentDetail({
        contentId,
        episodeId,
        content,
        isCard,
        dispatch
      });

      let timecodes = [];

      if (
        isVideoIndexingDataByContentId &&
        window.AiactivSDK &&
        window.AiactivSDK.getVideoIndexingDataByContentId
      ) {
        try {
          const sdkResponse = await window.AiactivSDK.getVideoIndexingDataByContentId(
            episodeId || contentId
          );
          timecodes = sdkResponse?.timecodes || [];
        } catch (sdkError) {
          console.error('Error fetching SDK video indexing data:', sdkError);
        }
      }

      const result: any = {
        type: ACTION_GET_CONTENT_DETAIL_ID,
        payload: {
          ...res,
          data: {
            ...res.data,
            timecodes
          }
        }
      };

      return dispatch(result);
    } catch (error) {
      console.error('Error fetching content detail:', error);
    }
  };
const getContent =
  ({
    slug,
    recommendation_id,
    recommendation_type,
    accessToken,
    profileToken,
    ssr,
    ipAddress,
    userAgent,
    origin,
    isGlobal
  }: any) =>
  (dispatch: any) =>
    DetailApi.getContentBySlug({
      slug,
      recommendation_id,
      recommendation_type,
      accessToken,
      profileToken,
      ssr,
      ipAddress,
      userAgent,
      isGlobal,
      origin
    })?.then(async (res) => {
      const data = res?.data || {};
      const result: any = {
        type: GET_CONTENT,
        payload: res,
        slug
      };
      if (res?.httpCode === HTTP_CODE.BLOCKED_ACCOUNT) {
        Router.push(`/${window?.location?.search || ''}`);
        dispatch(openPopup({ name: POPUP.NAME.BLOCK_ACCOUNT }));
      }
      let tVodInfo = null;
      let tvod = null;
      let triggers = data?.triggers;
      if (res?.data?.isPremiumTVod) {
        const isLiveEvent = get(res, 'data.tvod.isLiveEvent', false);
        const isSimulcast = get(res, 'data.tvod.isSimulcast', false);
        tVodInfo = await PaymentApi.getTVodInfo({
          contentId: data?.id,
          contentType: data?.type,
          isSimulcast,
          isLiveEvent,
          profileToken,
          accessToken
        });
        tvod = parseTVodFromInfo(tVodInfo);
        triggers = Triggers({ ...res?.data, tvod });
        data.tVodInfo = tVodInfo;
        data.tvod = tvod;
        data.triggers = triggers;
      }
      dispatch({ ...result, payload: { ...res, data } });
      return res;
    });

const getContentEpisode =
  ({ slug, accessToken, profileToken, ssr, ipAddress, userAgent, isGlobal, origin }: any) =>
  (dispatch: any) =>
    DetailApi.getContentBySlug({
      slug,
      accessToken,
      profileToken,
      ssr,
      ipAddress,
      userAgent,
      isGlobal,
      origin
    })?.then((res: any) => {
      const result: any = {
        type: GET_CONTENT_EPISODE,
        payload: res,
        slug
      };
      return dispatch(result);
    });

const getEpisodeListBySlug =
  ({ slug, page, limit, isGlobal }: any) =>
  (dispatch: any) =>
    DetailApi.getEpisodeListBySlug({ slug, page, limit, isGlobal })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_EPISODE_LIST_SUCCESS, res?.data))
    );

export const getRibbonDetailNotFound =
  ({ accessToken, profileToken, ssr, ipAddress, userAgent, isGlobal, origin }: any) =>
  (dispatch: any) =>
    ContentApi.getRibbonDetailNotFound({
      accessToken,
      profileToken,
      ssr,
      ipAddress,
      userAgent,
      isGlobal,
      origin
    })?.then((res: any) =>
      dispatch({
        type: ACTION_DETAIL_RIBBON_NOTFOUND,
        payload: res
      })
    );

const getContentRelated =
  ({ contentSlug, page, limit }: any) =>
  (dispatch: any) =>
    ContentApi.getContentRelated({ contentSlug, page, limit })
      ?.then((res: any) => {
        const result: any = {
          type: ACTION_GET_CONTENT_RELATED,
          payload: res,
          slug: contentSlug,
          page
        };
        return dispatch(result);
      })
      .catch((error) => error.response);

const getEpisodeListVod =
  ({
    slug,
    page,
    limit,
    accessToken,
    profileToken,
    content,
    ssr,
    ipAddress,
    userAgent,
    isGlobal,
    origin
  }: any) =>
  (dispatch: any) =>
    DetailApi.getEpisodeListBySlug({
      slug,
      page,
      limit,
      accessToken,
      profileToken,
      content,
      ssr,
      ipAddress,
      userAgent,
      isGlobal,
      origin
    })
      ?.then((res: any) => {
        const result: any = {
          type: ACTION_GET_EPISODE_LIST_VOD,
          payload: res,
          slug
        };
        return dispatch(result);
      })
      .catch((error) => error.response);

const getRelatedVod =
  ({ slug, accessToken, profileToken, ssr, ipAddress, userAgent, isGlobal, origin }: any) =>
  (dispatch: any) =>
    DetailApi.getRelatedVod({
      slug,
      accessToken,
      profileToken,
      ssr,
      ipAddress,
      userAgent,
      isGlobal,
      origin
    })
      ?.then((res: any) => {
        const result: any = {
          type: ACTION_GET_RELATED_VOD,
          payload: res
        };
        return dispatch(result);
      })
      .catch((error) => error.response);

const getRecommendVod =
  ({ slug, accessToken, profileToken, ssr, ipAddress, userAgent, isGlobal, origin }: any) =>
  (dispatch: any) =>
    DetailApi.getRecommendVod({
      slug,
      accessToken,
      profileToken,
      ssr,
      ipAddress,
      userAgent,
      isGlobal,
      origin
    })
      ?.then((res: any) => {
        const result: any = {
          type: ACTION_GET_RECOMMEND_VOD,
          payload: res
        };
        return dispatch(result);
      })
      .catch((error) => error.response);

const contentRecommendVod =
  ({ contentSlug, page, limit }: any) =>
  (dispatch: any) =>
    ContentApi.contentRecommendVod({ contentSlug, page, limit })
      ?.then((res: any) => {
        const result: any = {
          type: ACTION_GET_CONTENT_RELATED_VOD,
          payload: res,
          slug: contentSlug,
          page
        };
        dispatch(result);
        return result;
      })
      .catch((error) => error.response);

const getContentPopver =
  ({ contentId, epsId }: any) =>
  (dispatch: any) =>
    ContentApi.getContentPopver({ contentId, epsId })?.then((res: any) => {
      const result: any = {
        type: ACTION_GET_CONTENT_POPVER,
        payload: res,
        contentId,
        epsId
      };
      return dispatch(result);
    });

export const getContentWatchlater =
  ({ page = 0, limit = 30, accessToken, ribbonOrder, ribbonId, isGlobal }: any) =>
  (dispatch: any) =>
    ContentApi.getContentWatchlater({
      page,
      limit,
      accessToken,
      ribbonOrder,
      ribbonId,
      isGlobal
    })?.then((res: any) =>
      dispatch({
        type: ACTION_GET_CONTENT_WATCHLATER,
        payload: res,
        page
      })
    );

export const getContentWatchmore: any =
  ({ page = 0, limit = 30, accessToken, ribbonOrder, ribbonId, ribbonName, isGlobal }: any) =>
  (dispatch: any) =>
    ContentApi.getContentWatchmore({
      page,
      limit,
      accessToken,
      ribbonOrder,
      ribbonId,
      ribbonName,
      isGlobal
    })?.then((res: any) =>
      dispatch({
        type: ACTION_GET_CONTENT_WATCHMORE,
        payload: res
      })
    );

const addWatchLater = (item: any) => (dispatch: any) => {
  const action = {
    type: ADD_WATCH_LATER,
    payload: { data: item }
  };
  dispatch(action);
};

const delContentWatchmore = () => (dispatch: any) =>
  ContentApi.delContentWatchmore()?.then((res: any) => {
    const action = {
      type: REMOVE_WATCH_MORE
    };
    dispatch(action);
    return res.data;
  });

const resetContent = () => (dispatch: any) => {
  dispatch({ type: RESET_CONTENT });
};
const getContentService =
  ({ contentId, page, size }: any) =>
  (dispatch: any) =>
    ContentApi.getContentService({ contentId, page, size })?.then((res: any) => {
      dispatch({ type: GET_CONTENT_SERVICE, payload: { data: res } });
    });

const postRegisterConsultationViaPhone =
  ({ contentId, objectId, phoneNumber, isDeleteNumberPhone }: any) =>
  (dispatch: any) =>
    ContentApi.registerConsultationViaPhone({
      contentId,
      objectId,
      phoneNumber,
      isDeleteNumberPhone,
      dispatch
    })?.then((res: any) => {
      if (!res) {
        dispatch(setToast({ message: TEXT.MSG_ERROR }));
        return dispatch({ type: ACTION_TYPE.REGISTER_CONSULTATION_FAIL });
      }
      return dispatch({ type: ACTION_TYPE.REGISTER_CONSULTATION, payload: { data: res } });
    });

const getDataProductBrand = (data: any) => (dispatch: any) => {
  dispatch({ type: GET_DATA_PRODUCT_BRAND, payload: { data } });
};
const setTypeActionVideoIndexing = (data: any) => (dispatch: any) => {
  dispatch({ type: SET_TYPE_ACTION_VIDEO_INDEXING, payload: { data } });
};

const clearRegisterConsultation = () => (dispatch: any) => {
  dispatch({ type: ACTION_TYPE.CLEAR_REGISTER_CONSULTATION });
};
const setSessionId = (id: any) => (dispatch: any) => {
  dispatch({ type: ACTION_TYPE.SET_SESSION_ID_VI_INDEXING, payload: { data: id } });
};
const getItemIndicator = (data: any) => (dispatch: any) => {
  dispatch({ type: ACTION_TYPE.GET_ITEM_INDICATOR, payload: { data } });
};
const setSessionPlay =
  ({ titleId, seasonId, episodeId, titleName, contentConcurrentGroup, accessToken }: any) =>
  (dispatch: any) =>
    ContentApi.setSessionPlay({
      titleId,
      seasonId,
      episodeId,
      titleName,
      contentConcurrentGroup,
      dispatch,
      accessToken
    })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.SET_SESSION_PLAY, res));
      return res;
    });
const refreshSessionPlay: any = (sessionToken: any) => (dispatch: any) =>
  ContentApi.refreshSessionPlay(sessionToken)?.then((res: any) => {
    dispatch(createAction(ACTION_TYPE.REFRESH_SESSION_PLAY, res));
    return res;
  });
const endSessionPlay: any = (sessionToken: any) => (dispatch: any) => {
  if (!sessionToken) return;
  ContentApi.endSessionPlay(sessionToken)?.then((res: any) => {
    if (res?.success && res?.result?.success) {
      dispatch(createAction(ACTION_TYPE.SET_SESSION_PLAY, {}));
      dispatch(createAction(ACTION_TYPE.REFRESH_SESSION_PLAY, {}));
    }
  });
};
const statusEndScreenVod: any =
  (status = false) =>
  (dispatch: any) => {
    dispatch(createAction(ACTION_TYPE.SET_STATUS_END_SCREEN_VOD, status));
  };

export {
  GET_CONTENT_SERVICE,
  GET_CONTENT,
  RESET_CONTENT,
  GET_CONTENT_EPISODE,
  ACTION_GET_CONTENT_DETAIL,
  ACTION_GET_CONTENT_RELATED,
  ACTION_GET_CONTENT_POPVER,
  ACTION_GET_CONTENT_WATCHLATER,
  ACTION_GET_CONTENT_WATCHMORE,
  EPISODE_LIST_DATA,
  ACTION_GET_CONTENT_RELATED_VOD,
  ADD_WATCH_LATER,
  REMOVE_WATCH_LATER,
  REMOVE_WATCH_MORE,
  ACTION_DETAIL_RIBBON_NOTFOUND,
  ACTION_GET_RELATED_VOD,
  ACTION_GET_RECOMMEND_VOD,
  ACTION_GET_EPISODE_LIST_VOD,
  ACTION_GET_CONTENT_DETAIL_ID,
  GET_LIST_COMMENTS_SUCCESS,
  GET_DATA_PRODUCT_BRAND,
  SET_TYPE_ACTION_VIDEO_INDEXING,
  ACTION_GET_CONTENT_BY_SLUG_SEO,
  ACTION_GET_LIST_EPISODES_CONTENT,
  getListComments,
  getContentDetailByID,
  getEpisodeListVod,
  getRecommendVod,
  getRelatedVod,
  addWatchLater,
  getContent,
  getContentEpisode,
  getEpisodeListBySlug,
  getContentRelated,
  getContentPopver,
  delContentWatchmore,
  contentRecommendVod,
  resetContent,
  getContentService,
  postRegisterConsultationViaPhone,
  getDataProductBrand,
  setTypeActionVideoIndexing,
  clearRegisterConsultation,
  getItemIndicator,
  setSessionId,
  setSessionPlay,
  refreshSessionPlay,
  endSessionPlay,
  statusEndScreenVod
};
