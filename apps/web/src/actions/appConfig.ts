import UserApi from '@apis/userApi';
import { CONFIG_KEY } from '@constants/constants';
import { ACTION_TYPE, createAction } from '@actions/actionType';

const getContentConfig = () => (dispatch: any) => {
  UserApi.getConfig({ key: CONFIG_KEY.WEB_CONTENT_CONFIG })?.then((res: any) => {
    dispatch(createAction(ACTION_TYPE.GET_CONTENT_CONFIG_SUCCESS, res));
  });
};
const getTriggerConfig = () => (dispatch: any) => {
  UserApi.getConfig({ key: CONFIG_KEY.WEB_TRIGGER_CONFIG })?.then((res: any) => {
    dispatch(createAction(ACTION_TYPE.GET_TRIGGER_CONFIG_SUCCESS, res));
  });
};

const getPlayerConfig = () => (dispatch: any) => {
  UserApi.getConfig({ key: CONFIG_KEY.PLAYER_CONFIG })?.then((res: any) =>
    dispatch(createAction(ACTION_TYPE.GET_PLAYER_CONFIG_SUCCESS, res))
  );
};

export { getContentConfig, getTriggerConfig, getPlayerConfig };
