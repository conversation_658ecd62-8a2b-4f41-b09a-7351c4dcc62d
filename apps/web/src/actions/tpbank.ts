import Tpbank<PERSON>pi from '@apis/tpbank/tpbankApi';

const ACTION_TPBANK_CHECK_PHONE_NUMBER = 'ACTION_TPBANK_CHECK_PHONE_NUMBER';
const ACTION_TPBANK_CREATE_ACCOUNT_BY_PHONE = 'ACTION_TPBANK_CREATE_ACCOUNT_BY_PHONE';
const ACTION_TPBANK_LOGIN_BY_PHONE = 'ACTION_TPBANK_LOGIN_BY_PHONE';
const ACTION_TPBANK_GET_BILLING_PACKAGE = 'ACTION_TPBANK_GET_BILLING_PACKAGE';
const ACTION_TPBANK_CREATE_TRANSACTION = 'ACTION_TPBANK_CREATE_TRANSACTION';
const ACTION_TPBANK_UPDATECONFIG = 'ACTION_TPBANK_UPDATECONFIG';
const checkAccountExist = (phoneNumber: any, callback: any) => (dispatch: any) =>
  TpbankApi.checkAccountExist({ phoneNumber })?.then((res: any) => {
    if (res?.success) {
      const result: any = {
        type: ACTION_TPBANK_CHECK_PHONE_NUMBER,
        data: res?.data ?? {}
      };
      dispatch(result);
    }
    if (callback && typeof callback === 'function') {
      callback(res);
    }
    return res;
  });
const createAccountByPhone = (phoneNumber: any, callback: any) => (dispatch: any) =>
  TpbankApi.createAccountByPhone({ phoneNumber })?.then((res: any) => {
    if (res?.success) {
      const result: any = {
        type: ACTION_TPBANK_CREATE_ACCOUNT_BY_PHONE,
        data: res?.data ?? {}
      };
      dispatch(result);
    }
    if (callback && typeof callback === 'function') {
      callback(res);
    }
    return res;
  });
const loginByPhone = (phoneNumber: any, password: any, callback: any) => (dispatch: any) =>
  TpbankApi.loginByPhone({ phoneNumber, password })?.then((res: any) => {
    if (res?.success) {
      const result: any = {
        type: ACTION_TPBANK_LOGIN_BY_PHONE,
        data: res?.data ?? {}
      };
      dispatch(result);
    }
    if (callback && typeof callback === 'function') {
      callback(res);
    }
    return res;
  });
const getBillingPackage = (callback: any) => (dispatch: any) =>
  TpbankApi.getBillingPackage()?.then((res: any) => {
    if (res?.success) {
      const result: any = {
        type: ACTION_TPBANK_GET_BILLING_PACKAGE,
        data: res?.data?.items ?? []
      };
      dispatch(result);
    }
    if (callback && typeof callback === 'function') {
      callback(res);
    }
    return res;
  });
const createTransaction =
  (
    { userId, packageId, userTokenTpbank, diviceCodeTpBank, platform, promotionCode }: any,
    callback: any
  ) =>
  (dispatch: any) =>
    TpbankApi.createTransaction({
      userId,
      packageId,
      userTokenTpbank,
      diviceCodeTpBank,
      platform,
      promotionCode
    })?.then((res: any) => {
      if (res?.success) {
        const result: any = {
          type: ACTION_TPBANK_CREATE_TRANSACTION,
          data: res?.data ?? {}
        };
        dispatch(result);
      }
      if (callback && typeof callback === 'function') {
        callback(res);
      }
      return res;
    });
export {
  ACTION_TPBANK_CHECK_PHONE_NUMBER,
  ACTION_TPBANK_CREATE_ACCOUNT_BY_PHONE,
  ACTION_TPBANK_LOGIN_BY_PHONE,
  ACTION_TPBANK_GET_BILLING_PACKAGE,
  ACTION_TPBANK_CREATE_TRANSACTION,
  ACTION_TPBANK_UPDATECONFIG,
  checkAccountExist,
  createAccountByPhone,
  loginByPhone,
  getBillingPackage,
  createTransaction
};
