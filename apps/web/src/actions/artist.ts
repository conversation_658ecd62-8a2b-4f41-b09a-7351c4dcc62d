import Artist<PERSON><PERSON> from '@apis/cm/ArtistApi';

const ACTION_GET_INFO_ARTIST = 'ARTIST_GET_INFO';
const ACTION_GET_CONTENT_ARTIST = 'ARTIST_GET_CONTENT';
const ACTION_GET_ARTIST_RELATED = 'GET_ARTIST_RELATED';

const getInfoArtist =
  ({ slug, ssr, ipAddress, userAgent, origin }: any) =>
  (dispatch: any) =>
    ArtistApi.getInfoArtist({ slug, ssr, ipAddress, userAgent, origin })?.then((res: any) => {
      const result: any = {
        type: ACTION_GET_INFO_ARTIST,
        payload: res,
        slug
      };
      return dispatch(result);
    });
const getContentArtist =
  ({ slug, page, limit, sort, accessToken, ssr, ipAddress, userAgent, isGlobal, origin }: any) =>
  (dispatch: any) =>
    ArtistApi.getContentArtist({
      slug,
      page,
      limit,
      sort,
      accessToken,
      ssr,
      ipAddress,
      userAgent,
      isGlobal,
      origin
    })?.then((res: any) => {
      const result: any = {
        type: ACTION_GET_CONTENT_ARTIST,
        payload: res,
        slug,
        page,
        sort
      };
      return dispatch(result);
    });
const getArtistRelated =
  ({ slug, page, limit }: any) =>
  (dispatch: any) =>
    ArtistApi.getArtistRelated({ slug, page, limit })?.then((res: any) => {
      const result: any = {
        type: ACTION_GET_ARTIST_RELATED,
        payload: res,
        slug,
        page
      };
      return dispatch(result);
    });

export {
  ACTION_GET_INFO_ARTIST,
  ACTION_GET_CONTENT_ARTIST,
  ACTION_GET_ARTIST_RELATED,
  getInfoArtist,
  getContentArtist,
  getArtistRelated
};
