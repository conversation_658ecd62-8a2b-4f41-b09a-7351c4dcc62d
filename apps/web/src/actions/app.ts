import User<PERSON><PERSON> from '@apis/userApi';
import { CONFIG_KEY } from '@constants/constants';
import CMApi from '@apis/cmApi';
import { ACTION_TYPE, createAction } from './actionType';
const setTokenProfile = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_TOKEN_PROFILE, data));
const setViewPort = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_VIEW_PORT, data));
const setToken = (token: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_TOKEN, token));
const setDeviceInfo = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_DEVICE_INFO, data));
const setDeviceId = (deviceId: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_DEVICE_ID, deviceId));
const setLoading = (loading: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_LOADING, loading));
const setHideClickLater = (status: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_HIDE_CLICK_LATER_BIND_ACCOUNT, status));
const setLoadedData = (isLoaded: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_LOADED_DATA, isLoaded));

const getFaqsConfig =
  ({ ssr, key }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_FAQS, res))
    );

const getPrivacyConfig =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_PRIVACY, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_PRIVACY, res))
    );

const getPrivacyConfig120424 =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_PRIVACY_120424, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_PRIVACY_120424, res))
    );

const getAboutUsConfig =
  ({ ssr, key }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_ABOUT_US, { data: res, key }))
    );

const getUsageConfig =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_USAGE, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_USAGE, res))
    );
const getUsageV1Config =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_USAGE_V1, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_USAGE_V1, res))
    );
const getUsageV2Config =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_USAGE_V2, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_USAGE_V2, res))
    );

const getUsageV3Config =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_USAGE_V3, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_USAGE_V3, res))
    );

const getUsageV4Config =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_USAGE_V4, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_USAGE_V4, res))
    );
const getAnnounceConfig =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_ANNOUNCE, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.WEB_ANNOUNCE, res))
    );

const getAgreementConfig =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_AGREEMENT, ssr })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_AGREEMENT, res));
    });

const getAgreementConfig120424 =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_AGREEMENT_120424, ssr })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_AGREEMENT_120424, res));
    });

const getLicenseConfig =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_LICENSE, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_LICENSE, res))
    );
const getPolicyCancellationConfig =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_POLICY_CANCELLATION, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.WEB_POLICY_CANCELLATION, res))
    );
const getRegulationConfig =
  ({ ssr }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_REGULATION, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.WEB_REGULATION, res))
    );

const setOffBindAccount =
  (status = false) =>
  (dispatch: any) =>
    dispatch(createAction(ACTION_TYPE.OFF_BIND_ACCOUNT, status));

const getWebConfig =
  ({ ssr, globalHost }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({ key: CONFIG_KEY.WEB_CONFIG, ssr })?.then((res: any) => {
      const {
        sport,
        floatButton,
        outStreamAds,
        social,
        featureFlag,
        bindAccount,
        registrationTrigger,
        paymentConversion,
        configPersonalizationFlow,
        introPackages,
        introPackagesGlobal
      } = res || {};

      const filteredOutStreamAds = outStreamAds;

      const filteredOutStreamAdsNewConfig = Object.fromEntries(
        Object.entries(filteredOutStreamAds).filter(([key]) => {
          return ![
            'welcomeHome',
            'welcomeMainMenu',
            'welcomeIntro',
            'masterBanner',
            'bannerRibbonAds',
            'promotedRibbon',
            'inpage',
            'lobby',
            'welcomeHome_US',
            'welcomeMainMenu_US',
            'welcomeIntro_US'
          ].includes(key);
        })
      );

      // const filteredOutStreamAdsNewConfig = Object.fromEntries(
      //   Object.entries(filteredOutStreamAds).filter(([key]) => {
      //     return ![
      //       'welcomeHome',
      //       'welcomeMainMenu',
      //       'welcomeIntro',
      //       'masterBanner',
      //       'bannerRibbonAds',
      //       'promotedRibbon',
      //       'inpage',
      //       'lobby'
      //     ].includes(key);
      //   })
      // );
      dispatch(createAction(ACTION_TYPE.SET_COMPETITIONS, sport?.competitions));
      dispatch(createAction(ACTION_TYPE.SET_FLOAT_BUTTON, floatButton));
      dispatch(createAction(ACTION_TYPE.SET_SOCIAL_CONFIG, social));
      dispatch(createAction(ACTION_TYPE.SET_FEATURES_FLAG, featureFlag));
      dispatch(createAction(ACTION_TYPE.SET_BIND_ACCOUNT_CONFIG, bindAccount));
      dispatch(createAction(ACTION_TYPE.SET_REGISTRATION_TRIGGER_CONFIG, registrationTrigger));
      dispatch(createAction(ACTION_TYPE.SET_PAYMENT_CONVERSION_CONFIG, paymentConversion));
      dispatch(createAction(ACTION_TYPE.SET_PERSONALIZATION_FLOW, configPersonalizationFlow));
      dispatch(createAction(ACTION_TYPE.SET_OUT_STREAM_ADS, filteredOutStreamAdsNewConfig));
      dispatch(createAction(ACTION_TYPE.WEB_CONFIG, res));

      dispatch(
        createAction(
          ACTION_TYPE.SET_DATA_INTRO_PACKAGES,
          globalHost ? introPackagesGlobal : introPackages
        )
      );
    });

const getGeoCheck =
  ({ ssr, ipAddress, globalHost }: any) =>
  (dispatch: any) =>
    CMApi.geoCheck({ ssr, ipAddress, globalHost, dispatch })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GEO_CHECK, res?.data))
    );

const setToast = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_TOAST, data, dispatch));

const clearToast = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.CLEAR_TOAST, data, dispatch));
const setStatusLoadOutStreamAds = (status: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_STATUS_LOAD_OUT_STREAM_ADS, status));
const setStatusLoadMastheadAds = (status: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_STATUS_LOAD_MASTHEAD_ADS, status));

const setStatusOnBoarding =
  (status = false) =>
  (dispatch: any) =>
    dispatch(createAction(ACTION_TYPE.SET_STATUS_ONBOARDING, status));
const setStatusDownloadApp = (status: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_STATUS_DOWNLOAD_APP, status));

const setStatusDialogOnBoarding =
  (status = false) =>
  (dispatch: any) =>
    dispatch(createAction(ACTION_TYPE.SET_STATUS_DIALOG_ONBOARDING, status));

const setStatusPaymentConversion =
  (status = false) =>
  (dispatch: any) =>
    dispatch(createAction(ACTION_TYPE.SET_STATUS_PAYMENT_CONVERSION, status));

const setStatusTVodReminderScreen =
  (status = false) =>
  (dispatch: any) =>
    dispatch(createAction(ACTION_TYPE.SET_STATUS_TVOD_REMINDER_SCREEN, status));

const setStatusCompanionBanner =
  (status = false) =>
  (dispatch: any) =>
    dispatch(createAction(ACTION_TYPE.SET_STATUS_COMPANION_BANNER, status));

export {
  getAboutUsConfig,
  getRegulationConfig,
  getPolicyCancellationConfig,
  setTokenProfile,
  setDeviceInfo,
  setStatusDownloadApp,
  setViewPort,
  setOffBindAccount,
  setLoading,
  setToken,
  setDeviceId,
  setLoadedData,
  setHideClickLater,
  getFaqsConfig,
  getPrivacyConfig,
  getUsageConfig,
  getUsageV1Config,
  getUsageV2Config,
  getUsageV3Config,
  getUsageV4Config,
  getAnnounceConfig,
  getAgreementConfig,
  getLicenseConfig,
  getGeoCheck,
  getWebConfig,
  setToast,
  clearToast,
  setStatusLoadOutStreamAds,
  setStatusLoadMastheadAds,
  setStatusOnBoarding,
  setStatusDialogOnBoarding,
  setStatusPaymentConversion,
  setStatusTVodReminderScreen,
  setStatusCompanionBanner,
  getAgreementConfig120424,
  getPrivacyConfig120424
};
