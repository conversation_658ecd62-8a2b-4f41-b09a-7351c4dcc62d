import User<PERSON>pi from '@apis/userApi';

const ACTION_REGITER_MOBILE = 'REGITER_MOBILE';
const ACTION_RESEND_OTP_CODE_REGISTER = 'RESEND_OTP_CODE_REGISTER';
const registerMobile = (phoneNumber: any, givenName: any, password: any) => (dispatch: any) =>
  UserApi.registerMobile({ phoneNumber, givenName, password })
    ?.then((resp: any) => {
      const result: any = {
        type: ACTION_REGITER_MOBILE,
        payload: resp
      };

      return dispatch(result);
    })
    .catch((error: any) => error.response);

const confirmOtpRegisterMobile = (registerSessionId: any, otpCode: any) =>
  UserApi.confirmOtpRegisterMobile(registerSessionId, otpCode)?.then((resp: any) => resp.data);
const resendOtpCodeRegister = (phoneNumber: any) => (dispatch: any) =>
  UserApi.resendOtpCodeRegister(phoneNumber)
    ?.then((resp: any) => {
      const result: any = {
        type: ACTION_RESEND_OTP_CODE_REGISTER,
        payload: resp
      };

      return dispatch(result);
    })
    .catch((error: any) => error.response);

export {
  ACTION_REGITER_MOBILE,
  ACTION_RESEND_OTP_CODE_REGISTER,
  registerMobile,
  confirmOtpRegisterMobile,
  resendOtpCodeRegister
};
