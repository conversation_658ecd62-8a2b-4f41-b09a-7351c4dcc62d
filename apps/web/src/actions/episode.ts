import DetailApi from '@apis/detailApi';
import { createAction, ACTION_TYPE } from './actionType';
const clearEpisodeList = () => createAction(ACTION_TYPE.CLEAR_EPISODE_LIST);
const setEpisodeListSuccess = (data: any) =>
  createAction(ACTION_TYPE.GET_EPISODE_LIST_SUCCESS, data);

const getEpisodeBySlug: any =
  ({ slug, page, limit, isGlobal }: any) =>
  (dispatch: any) =>
    DetailApi.getEpisodeListBySlug({ slug, page, limit, isGlobal })?.then((res: any) =>
      dispatch(setEpisodeListSuccess({ ...res, slug }))
    );

const setEpisode: any = (episode: any) => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.SET_EPISODE, episode));
};

const setNextEpisode: any = (episode: any) => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.SET_NEXT_EPISODE, episode));
};

export { getEpisodeBySlug, clearEpisodeList, setEpisode, setNextEpisode };
