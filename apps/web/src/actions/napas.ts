import PaymentV2Api from '@apis/PaymentV2';
import TrackingPayment from '@tracking/functions/payment';
import { createTimeout } from '@helpers/common';
import { EL_ID, PAGE, KEY_NAPAS_RESULT_SMART_TV, KEY_NAPAS_RESULT_WEB } from '@constants/constants';
import {
  DOMAIN_WEB,
  NAPAS_CHANNEL,
  NAPAS_SOURCE_OF_FUNDS_TYPE,
  SDK_NAPAS,
  ENABLE_SDK_NAPAS
} from '@config/ConfigEnv';
import { TEXT } from '@constants/text';
import ConfigCookie from '@config/ConfigCookie';
import { createTransactionSuccess } from './payment';
import { setLoading } from '@actions/app';
import { ACTION_TYPE, createAction } from './actionType';

const trackingPayment = new TrackingPayment();
let timerConfirmTransaction: any = null;
const TIME_CONFIRM_TRANSACTION = 300000;

const createTransaction =
  ({
    tokenId,
    selectedTerm,
    selectedMethod,
    promotionData,
    queryString,
    router,
    valueReferralCode,
    isSegmentedUser
  }: any) =>
  (dispatch: any) =>
    PaymentV2Api.createTransaction({
      paymentMethod: 'Card',
      paymentService: 'Napas',
      packageId: selectedTerm?.id,
      promotionCode: promotionData?.promotionCode || selectedTerm?.giftCode || '',
      tokenId
    })?.then((res: any) => {
      const result = res?.data?.result || {};
      trackingPayment.payButtonSelected({
        selectedTerm,
        promotionData,
        selectedMethod,
        transaction: result,
        isSegmentedUser
      });
      if (!res?.success) {
        dispatch(
          createAction(
            ACTION_TYPE.SET_TOAST,
            { message: res?.data?.error_message || TEXT.MSG_ERROR },
            dispatch
          )
        );
      } else if (res?.data?.error_code === 1) {
        dispatch(
          createAction(
            ACTION_TYPE.SET_TOAST,
            {
              message: res?.data?.error_message || TEXT.MSG_ERROR
            },
            dispatch
          )
        );
      } else {
        dispatch(createTransactionSuccess({ data: result, valueReferralCode }));
        dispatch(
          getInfoTransaction({
            orderId: result?.order_id,
            returnUrl: `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}${queryString}&orderId=${result?.order_id}&merchantId=VIEON`,
            router
          })
        );
      }
      return result;
    });

const getInfoTransaction =
  ({ orderId, returnUrl, router, isSmartTv }: any) =>
  (dispatch: any) => {
    dispatch(setLoading(true));
    PaymentV2Api.getInfoTransaction({ orderId, returnUrl })?.then((res: any) => {
      dispatch(setLoading(false));
      if (!res?.success) {
        if (res?.httpCode === 404) {
          dispatch(
            createAction(ACTION_TYPE.SET_TOAST, { message: TEXT.TRANSACTION_NOT_FOUND }, dispatch)
          );
        } else {
          dispatch(
            createAction(
              ACTION_TYPE.SET_TOAST,
              { message: res?.data?.error_message || TEXT.MSG_ERROR },
              dispatch
            )
          );
        }
      } else {
        const result = res?.data?.result || {};
        if (result?.flow === 1) {
          createFormSubmit({ ...result?.data, orderId, returnUrl });
        } else if (result?.flow === 2) {
          verify3DSecure(result?.data?.html, dispatch);
        } else if (result?.flow === 0) {
          if (isSmartTv) {
            router.push(PAGE.SMART_TV_RESULT_PAYMENT, `/smart-tv/${result?.order}/ket-qua`);
          } else {
            router.push(`/mua-goi/ket-qua/?orderId=${result?.order}`);
          }
        }
      }
      dispatch(
        createAction(ACTION_TYPE.GET_INFO_TRANSACTION, { ...res?.data, isSuccess: res?.success })
      );
    });
  };

const getResultTransaction =
  ({ data, checksum, orderId, isSmartTv }: any) =>
  (dispatch: any) =>
    PaymentV2Api.getResultTransaction({ data, checksum, orderId, paymentMethod: 'Card' })?.then(
      (res) => {
        if (timerConfirmTransaction) clearTimeout(timerConfirmTransaction);
        if (res?.success) {
          if (isSmartTv) {
            ConfigCookie.remove(KEY_NAPAS_RESULT_SMART_TV);
          } else {
            ConfigCookie.remove(KEY_NAPAS_RESULT_WEB);
          }
        } else if (res?.httpCode === 404) {
          dispatch(
            createAction(ACTION_TYPE.SET_TOAST, { message: TEXT.TRANSACTION_NOT_FOUND }, dispatch)
          );
        } else {
          timerConfirmTransaction = createTimeout(() => {
            const napasResult = isSmartTv
              ? ConfigCookie.load(KEY_NAPAS_RESULT_SMART_TV)
              : ConfigCookie.load(KEY_NAPAS_RESULT_WEB);
            const dataCookie = JSON.parse(napasResult || '{}');
            dispatch(
              getResultTransaction({
                data: data || dataCookie?.data,
                checksum: checksum || dataCookie?.checksum,
                orderId: orderId || dataCookie?.orderId
              })
            );
          }, TIME_CONFIRM_TRANSACTION);
        }
        dispatch(createAction(ACTION_TYPE.CONFIRM_RESULT_TRANSACTION, res));
      }
    );

const getStatusTransaction =
  ({ orderId }: any) =>
  (dispatch: any) =>
    PaymentV2Api.getStatusTransaction({ orderId })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.SET_TRANSACTION_RESULT, res))
    );

const createFormSubmit = ({
  returnUrl,
  merchant_id,
  data_key,
  napas_key,
  api_operation,
  client_ip,
  device_id,
  environment,
  card_scheme,
  enable_3Dsecure,
  orderId,
  orderAmount,
  order_reference,
  orderCurrency
}: any) => {
  // Check if SDK is disabled using type-safe comparison
  if (!ENABLE_SDK_NAPAS || ENABLE_SDK_NAPAS === 'false' || String(ENABLE_SDK_NAPAS) === '0') return;
  const napasPaymentElm = document.getElementById(EL_ID.ID_ELM_NAPAS_PAYMENT);
  const formElm = document.createElement('form');
  formElm.id = 'merchant-form';
  formElm.method = 'POST';
  formElm.action = returnUrl;
  const napasWidget = document.createElement('div');
  napasWidget.id = 'napas-widget-container';
  const scriptElm = document.createElement('script');
  scriptElm.type = 'text/javascript';
  scriptElm.id = 'napas-widget-script';
  scriptElm.src = SDK_NAPAS;
  scriptElm.setAttribute('merchantId', merchant_id);
  scriptElm.setAttribute('clientIP', client_ip);
  scriptElm.setAttribute('deviceId', device_id);
  scriptElm.setAttribute('environment', environment);
  scriptElm.setAttribute('cardScheme', card_scheme);
  scriptElm.setAttribute('enable3DSecure', enable_3Dsecure);
  scriptElm.setAttribute('apiOperation', api_operation);
  scriptElm.setAttribute('orderAmount', orderAmount);
  scriptElm.setAttribute('orderCurrency', orderCurrency);
  scriptElm.setAttribute('orderReference', order_reference);
  scriptElm.setAttribute('orderId', orderId);
  scriptElm.setAttribute('channel', NAPAS_CHANNEL);
  scriptElm.setAttribute('sourceOfFundsType', NAPAS_SOURCE_OF_FUNDS_TYPE);
  scriptElm.setAttribute('dataKey', data_key);
  scriptElm.setAttribute('napasKey', napas_key);
  napasPaymentElm?.appendChild(formElm);
  napasPaymentElm?.appendChild(napasWidget);
  napasPaymentElm?.appendChild(scriptElm);
};

const verify3DSecure = (html: any, dispatch: any) => {
  if (!html) {
    dispatch(createAction(ACTION_TYPE.SET_TOAST, { message: TEXT.MSG_ERROR }, dispatch));
  } else {
    document.open();
    document.write(html);
    document.close();
  }
};

export { createTransaction, getInfoTransaction, getResultTransaction, getStatusTransaction };
