import ResultVotingApi from '@apis/ResultVoting';
import { ACTION_TYPE } from './actionType';

export const getListRoundsVoting = () => (dispatch: any) =>
  ResultVotingApi.getListRounds()?.then((res: any) => {
    const result: any = {
      type: ACTION_TYPE.GET_LIST_ROUNDS_VOTING,
      payload: res
    };
    return dispatch(result);
  });

export const getListEpisodesOfRound =
  ({ campaignId }: any) =>
  (dispatch: any) =>
    ResultVotingApi.getListEpisodesOfRound({ campaignId })?.then((res: any) => {
      const result: any = {
        type: ACTION_TYPE.GET_LIST_EPISODES_OF_ROUND,
        payload: res,
        campaignId
      };
      return dispatch(result);
    });

export const getDetailEpisodeOfRound =
  ({ questionId, index }: any) =>
  (dispatch: any) => {
    if (index === 0) {
      return ResultVotingApi.getDetailEpisodeOfFirstRound({ questionId })?.then((res: any) => {
        const result: any = {
          type: ACTION_TYPE.GET_DETAIL_EPISODE_IN_ROUND,
          payload: res,
          questionId
        };
        return dispatch(result);
      });
    }
    return ResultVotingApi.getDetailEpisodeOfOtherRound({ campaignId: questionId })?.then(
      (res: any) => {
        const result: any = {
          type: ACTION_TYPE.GET_DETAIL_EPISODE_IN_ROUND,
          payload: res,
          questionId
        };
        return dispatch(result);
      }
    );
  };

export const getListRatingOfEpisode =
  ({ questionId }: any) =>
  (dispatch: any) =>
    ResultVotingApi.getListRatingOfEpisode({ questionId })?.then((res: any) => {
      const result: any = {
        type: ACTION_TYPE.GET_LIST_RATING_OF_EPISODE,
        payload: res,
        questionId
      };
      return dispatch(result);
    });

export const getFinalResult =
  ({ questionId }: any) =>
  (dispatch: any) =>
    ResultVotingApi.getFinalResult({ questionId })?.then((res: any) => {
      const result: any = {
        type: ACTION_TYPE.GET_FINAL_RESULT,
        payload: res,
        questionId
      };
      return dispatch(result);
    });

export const getListCampaigns = () => (dispatch: any) =>
  ResultVotingApi.getListCampaignss()?.then((res: any) => {
    const result: any = {
      type: ACTION_TYPE.GET_LIST_CAMPAIGNS,
      payload: res
    };
    return dispatch(result);
  });

export const getListWinnersOfCampaign =
  ({ contentId, page, limit }: any) =>
  (dispatch: any) =>
    ResultVotingApi.getListWinnersOfCampaign({ contentId, page, limit })?.then((res: any) => {
      const result: any = {
        type: ACTION_TYPE.GET_LIST_WINNERS_OF_CAMPAIGNS,
        payload: res,
        contentId
      };
      return dispatch(result);
    });
