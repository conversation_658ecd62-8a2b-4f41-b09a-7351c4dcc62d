import { ACTION_TYPE, createAction } from '@actions/actionType';
import User<PERSON><PERSON> from '@apis/userApi';
import MultiPro<PERSON>le<PERSON>pi from '@apis/MultiProfile';
import { setToast, setTokenProfile } from '@actions/app';
import { openPopup } from '@actions/popup';
import { TEXT } from '@constants/text';
import { HTTP_CODE, LOBBY, PAGE, PIN_CODE, POPUP } from '@constants/constants';
import { removeAccessTokenProfile, saveAccessTokenProfile } from '@helpers/common';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
export const setLobbyStep = (step?: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_LOBBY_STEP, step));
export const setStatusLobbyProfile = (status?: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_STATUS_LOBBY_PROFILE, status));

export const getConfigAgeRanges = () => (dispatch: any) =>
  UserApi.getConfig({ key: LOBBY.AGE_RANGES })?.then((res: any) =>
    dispatch(createAction(ACTION_TYPE.GET_AGE_RANGES, res))
  );

export const getConfigAgeRangesKid = () => (dispatch: any) =>
  UserApi.getConfig({ key: LOBBY.KID_AGE_RANGES })?.then((res: any) =>
    dispatch(createAction(ACTION_TYPE.GET_AGE_RANGES_KID, res))
  );

export const getConfigGenders = () => (dispatch: any) =>
  UserApi.getConfig({ key: LOBBY.GENDERS })?.then((res: any) => {
    dispatch(createAction(ACTION_TYPE.GET_GENDERS, res));
  });
export const deleteProfileSuccess = (dataResult: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.DELETE_PROFILE, dataResult));
export const deleteProfile = (dataRequest: any) => (dispatch: any) =>
  MultiProfileApi.deleteProfile(dataRequest)?.then((res: any) => {
    if (res?.success) return dispatch(deleteProfileSuccess(res));
    if (res?.httpCode === HTTP_CODE.FAIL) {
      return dispatch(setToast({ message: res?.message || TEXT.MSG_ERROR }));
    }
  });

export const getAvatars = (callback?: any) => (dispatch: any) =>
  MultiProfileApi.getAvatars()?.then((res: any) => {
    if (res?.success) {
      dispatch(createAction(ACTION_TYPE.GET_AVATARS, res?.data?.result?.items));
    }
    if (typeof callback === 'function') {
      callback(res);
    }
  });

export const getProfile = (profileId: any) => (dispatch: any) =>
  MultiProfileApi.getProfileId(profileId)?.then((res: any) => {
    if (res?.success) return dispatch(createAction(ACTION_TYPE.GET_PROFILE_ID, res?.data?.result));
    dispatch(setToast({ message: res?.message || TEXT.MSG_ERROR }));
  });
export const setResultForm = (result: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.CACHE_FORM, result));
export const resetResultForm = (result: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.RESET_FORM, result));
export const setDefaultForm = (result: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.DEFAULT_FORM, result));
export const setAllStateMultiProfile = (result: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.RESET_ALL_STATE_MULTI_PROFILE, result));

export const selectedProfile =
  ({ data, callback, currentProfile, isReloadPage, notSaveProfile }: any) =>
  (dispatch: any) => {
    const { id, hasPinCode } = data || {};
    if (!id) return;

    const handleGetAccessTokenOfProfile = async ({ pinCode, handleError }: any) =>
      MultiProfileApi.getAccessTokenOfProfile({ id, pinCode })?.then((res: any) => {
        const accessToken = get(res, 'data.result.accessToken', '');
        if (res?.success && accessToken) {
          if (!isReloadPage) {
            dispatch(selectedProfileSuccess(data));
            dispatch(setTokenProfile(accessToken));
          }
          saveAccessTokenProfile(accessToken);
          if (pinCode) dispatch(openPopup());
          if (typeof callback === 'function') callback(); // call back to home page
        } else if (typeof handleError === 'function') handleError(res);
      });
    if (hasPinCode) {
      setTimeout(() => {
        dispatch(
          openPopup({
            name: POPUP.NAME.PIN_CODE,
            status: PIN_CODE.VERIFY,
            data,
            onSubmitForm: handleGetAccessTokenOfProfile
          })
        );
      }, 100);
    } else if (currentProfile?.isKid && !data?.isKid && !notSaveProfile) {
      dispatch(
        openPopup({
          name: POPUP.NAME.WARNING_KID_CHANGE_PROFILE_NON_KID,
          onConfirm: handleGetAccessTokenOfProfile
        })
      );
    } else {
      handleGetAccessTokenOfProfile({});
    }
  };
export const selectedProfileSuccess = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SELECTED_PROFILE, data));

export const getProfileSelected = (dataRequest: any) => (dispatch: any) =>
  MultiProfileApi.getProfileSelected(dataRequest)?.then((res: any) => {
    const { ssr, router } = dataRequest || {};
    if (res?.success && res?.data?.result?.id) {
      dispatch(selectedProfileSuccess(res?.data?.result));
    } else if (!ssr) {
      removeAccessTokenProfile();
      if (typeof router?.push === 'function') {
        router.push(PAGE.LOBBY_PROFILES);
      }
    }
  });

export const clearProfile = () => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.CLEAR_PROFILE));
};

export const getMultiProfile = (dataRequest?: any) => (dispatch: any) => {
  const { accessToken, idProfileAccount, hasToSelectProfileDefault, kid } = dataRequest || {};
  return MultiProfileApi.getMultiProfile({ accessToken, kid })?.then((res: any) => {
    if (accessToken) {
      const profileDefault = idProfileAccount
        ? (res?.items || []).find((item: any) => item?.id === idProfileAccount)
        : get(res, 'items[0]', {});
      if (!isEmpty(profileDefault) && (idProfileAccount || hasToSelectProfileDefault)) {
        dispatch(selectedProfile({ data: profileDefault }));
      }
    }
    if (kid) return dispatch(createAction(ACTION_TYPE.GET_MULTI_PROFILE_KID, res));
    return dispatch(createAction(ACTION_TYPE.GET_MULTI_PROFILE, res));
  });
};

export const updateAgreement = (accepted: any) => (dispatch: any) =>
  MultiProfileApi.updateAgreement({ accepted })?.then((res: any) => {
    if (res?.success) return dispatch(createAction(ACTION_TYPE.AGREEMENT, res));
  });

export const getExportActivityEmail = () => (dispatch: any) =>
  MultiProfileApi.getExportActivityEmail()?.then((res: any) =>
    dispatch(createAction(ACTION_TYPE.IS_EXPORT_EMAIL, res?.isExportEmail))
  );

export const exportActivityEmail =
  ({ isExportEmail, email }: any) =>
  (dispatch: any) =>
    MultiProfileApi.exportActivityEmail({ isExportEmail, email })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.POST_EXPORT_EMAIL, res?.data?.result));
      if (!res?.success) {
        dispatch(setToast({ message: TEXT.MSG_ERROR }));
      }
    });
export const getDetailWatchPending = (data: any) =>
  createAction(ACTION_TYPE.GET_DETAIL_WATCH_PENDING, data);

export const getDetailWatch =
  ({ profileId }: any) =>
  async (dispatch: any) => {
    dispatch(getDetailWatchPending(true));
    await MultiProfileApi.getDetailWatch({ profileId })?.then((res: any) => {
      if (res?.data) {
        dispatch(createAction(ACTION_TYPE.GET_DETAIL_WATCH, res));
      }
      dispatch(getDetailWatchPending(false));
    });
  };

export const getMoreDetailWatch =
  ({ profileId, offset }: any) =>
  async (dispatch: any) => {
    dispatch(getDetailWatchPending(true));
    await MultiProfileApi.getDetailWatch({ profileId, offset })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_MORE_DETAIL_WATCH, res));
      dispatch(getDetailWatchPending(false));
    });
  };
export const getRestrictionContent = (profileId: any) => async (dispatch: any) => {
  await MultiProfileApi.getRestrictionContent(profileId)?.then((res: any) => {
    if (res?.success) {
      dispatch(createAction(ACTION_TYPE.GET_RESTRICTION_CONTENT, res?.data?.result));
    }
  });
};

export const getRestrictedListOfKids =
  ({ contentId }: any) =>
  async (dispatch: any) => {
    await MultiProfileApi.getRestrictedListOfKids({ contentId })?.then((res: any) => {
      if (res?.httpCode === HTTP_CODE.OK_200) {
        dispatch(createAction(ACTION_TYPE.GET_RESTRICTED_LIST_OF_KIDS, res?.data?.result));
        if (!res?.data?.result?.items?.length) {
          dispatch(setToast({ message: TEXT.MESSAGE_JUST_UPDATED_LIST }));
          dispatch(openPopup({}));
          setTimeout(() => {
            window.location.reload();
          }, 150);
        }
      } else {
        dispatch(openPopup({}));
        dispatch(setToast({ message: TEXT.MESSAGE_CANNOT_SHOW_DATA }));
      }
    });
  };
export const clearRestrictedListOfKids = () => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.GET_RESTRICTED_LIST_OF_KIDS, {}));
export const isRemoveContentInTitleRestrictionFlow = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.IS_REMOVE_CONTENT_FLOW, data));
