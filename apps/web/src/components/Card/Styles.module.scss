.cardHover {
  @apply absolute z-50 shadow-md;

  &Container {
    @apply relative w-full h-full;
  }

  &Wrap {
    @apply flex flex-col pb-2 space-y-2 md:space-y-3.5 bg-vo-dim-gray-900 opacity-0 scale-0;
  }
}

.center {
  & > div > div {
    // @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
    @apply absolute;
  }
}

.top {
  &Center {
    & > div > div {
      // @apply absolute -top-1/2 left-1/4 -translate-x-[1%];
      @apply absolute;
    }
  }
  &Right {
    & > div > div {
      // @apply absolute top-0 right-0 -translate-x-1/2 -translate-y-1/2;
      @apply absolute;
    }
  }
  &Left {
    & > div > div {
      // @apply absolute -top-1/2 -left-1/2;
      @apply absolute;
    }
  }
}

.bottom {
  &Center {
    & > div > div {
      // @apply absolute bottom-0 -left-1/4 -translate-x-[1%];
      @apply absolute;
    }
  }
  &Right {
    & > div > div {
      // @apply absolute -right-1/4 -bottom-1/4;
      @apply absolute;
    }
  }
  &Left {
    & > div > div {
      // @apply absolute -left-1/2 -bottom-1/2;
      @apply absolute;
    }
  }
}

.middle {
  &Right {
    & > div > div {
      // @apply absolute -top-1/2 -right-1/2;
      @apply absolute;
    }
  }
  &Left {
    & > div > div {
      // @apply absolute -top-1/2 -left-1/2;
      @apply absolute;
    }
  }
}
