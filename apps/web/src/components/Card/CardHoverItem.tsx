import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import classNames from 'classnames';
import Billboard from '@components/Billboard/Billboard';
import CardHoverInfo from '@components/Card/CardHoverInfo';
import { openCardHover } from '@actions/popup';
import { getTipData } from '@actions/page';
import { CONTENT_TYPE, PAGE, RIBBON_TYPE } from '@constants/constants';
import { PLAYER_TYPE } from '@constants/player';
import { watchNowBehavior } from '@components/trigger/triggerFunction';
import { segmentEvent } from '@tracking/TrackingSegment';
import LocalStorage from '@config/LocalStorage';
import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import TrackingApp from '@tracking/functions/TrackingApp';
import { useMoveOutSideElement, useOutsideEvent, useVieRouter } from '@customHook';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import { AnimatePresence, motion } from 'framer-motion';
import { initAnimateScale, animateScale } from '@helpers/common';
import styles from './Styles.module.scss';

const CardHoverItem = () => {
  const wrapperRef = useRef<any>(null);
  const ref = useRef<any>(null);
  const dispatch = useDispatch();
  const router = useVieRouter();
  const { pathname, query } = router || {};
  const { webConfig, isMobile } = useSelector((state: any) => state?.App || {});
  const searchContents = useSelector((state: any) => state?.Search?.SEARCH_CONTENT);
  const expiredString = get(webConfig, 'tVod.text.expiredString', '');
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});
  const { tipData } = useSelector((state: any) => state?.Page || {});
  const { cardHover } = useSelector((state: any) => state?.Popup || {});
  const {
    id,
    groupId,
    title,
    type,
    index,
    contentTypeTvod,
    isLiveTv,
    isOriginal,
    isMain,
    categoryTracking,
    ribbonType,
    enableBanner,
    displayPosition
  } = cardHover || {};
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);

  const [isOpen, setOpen] = useState(false);

  const masterBannerData = useSelector((state: any) => {
    const dataMenu = state?.Menu?.activeSubMenu || state?.Menu?.activeMenu || {};
    return state?.Page?.pageBanner?.[dataMenu?.seo?.url];
  });
  const tipDataItem = useMemo(() => {
    if (ribbonType === RIBBON_TYPE.WATCH_MORE && type === CONTENT_TYPE.EPISODE) {
      return tipData?.[groupId];
    }
    return tipData?.[id];
  }, [tipData, cardHover]);
  const cardClassName = classNames(
    'intro intro--preview intro--preview-vod--mini',
    styles.cardHover,
    isOriginal && 'intro--original',
    isMain ? 'intro--variant' : '',
    enableBanner ? '!z-[10001]' : '',
    styles[`${displayPosition}`]
  );

  useEffect(
    () => () => {
      onClose();
    },
    []
  );

  useEffect(() => {
    if (id) {
      setOpen(true);
    }
    if (
      id &&
      (isEmpty(tipDataItem) ||
        (ribbonType === RIBBON_TYPE?.WATCH_MORE && type === CONTENT_TYPE?.EPISODE)) &&
      (type === CONTENT_TYPE?.SEASON ||
        type === CONTENT_TYPE?.EPISODE ||
        type === CONTENT_TYPE?.MOVIE)
    ) {
      if (ribbonType === RIBBON_TYPE?.WATCH_MORE && type === CONTENT_TYPE?.EPISODE) {
        dispatch(getTipData({ id: groupId }));
      } else {
        dispatch(getTipData({ id }));
      }
    }
  }, [id]);

  const handleSaveProgress = () => {
    const player: any = document.getElementById(PLAYER_TYPE.CARD_HOVER);
    if (player) {
      const currentTime = player?.ended ? 0 : player?.currentTime || 0;
      ConfigLocalStorage.set(
        LocalStorage.MINI_PLAYER_PROGRESS,
        JSON.stringify({ [id]: currentTime })
      );
    }
  };

  const onClose = () => {
    handleSaveProgress();
    setOpen(false);
    dispatch(openCardHover({}));
  };

  useOutsideEvent(wrapperRef, onClose);

  useMoveOutSideElement(ref, onClose);

  const onClickBanner = async () => {
    onClose();
    await watchNowBehavior({
      profile,
      currentProfile,
      contentData: cardHover,
      router,
      dispatch,
      isMobile,
      isGlobal
    });

    if (typeof cardHover?.onContentSelected === 'function') {
      cardHover.onContentSelected({ cardData: cardHover, contentTypeTvod });
    } else {
      TrackingApp.contentSelected({
        data: {
          ...cardHover,
          seasonThumb: cardHover?.images?.thumbnail,
          seasonGenre: cardHover?.genreText
        },
        masterBannerData,
        clickType: VALUE.HOVER_CLICK,
        isLiveTv,
        category: isLiveTv ? categoryTracking : undefined
      });
    }

    if (pathname === PAGE.SEARCH) {
      const searchItems = searchContents?.[query?.q]?.[-1]?.[0]?.items || [];
      const itemPosition = (searchItems || []).findIndex((it: any) => it?.id === id);
      segmentEvent(NAME.SELECT_SEARCH_RESULT, {
        [PROPERTY.KEYWORD]: query?.q,
        [PROPERTY.CONTENT_TITLE]: title,
        [PROPERTY.CONTENT_TYPE]: type,
        [PROPERTY.CONTENT_POSITION]: itemPosition || index || 0,
        [PROPERTY.CURRENT_PAGE]: window?.location?.href
      });
    }
  };

  const variants = {
    open: animateScale(displayPosition),
    closed: { scale: 0, opacity: 0 }
  };

  const bubbleStyles = {
    ...(cardHover?.bubbleStyles || {})
  };

  const bubbleContainerStyles = {
    ...(cardHover?.bubbleContainerStyles || {}),
    height: 'auto',
    display: 'flex'
  };

  if (isEmpty(cardHover)) return null;

  return (
    <div
      className={cardClassName}
      id={`card-hover_${id}`}
      onMouseLeave={onClose}
      ref={ref}
      style={bubbleStyles}
    >
      <div className={classNames('!bg-transparent', styles.cardHoverContainer)}>
        <AnimatePresence>
          <motion.div
            className={classNames(styles.cardHoverWrap)}
            initial={initAnimateScale(displayPosition)}
            animate={isOpen ? 'open' : 'closed'}
            exit={!isOpen ? 'closed' : ''}
            transition={{ duration: 0.45, delay: 0.45 }}
            variants={variants}
            style={bubbleContainerStyles}
            id="CARD_HOVER_ITEM"
            ref={wrapperRef}
          >
            <Billboard
              playerId={PLAYER_TYPE.CARD_HOVER}
              billboardData={cardHover}
              tipDataItem={tipDataItem}
              className="billboard--intro !w-full"
              onClickBanner={onClickBanner}
              isAnimateText
              canClickBillboard
              isCardDetail
              isCardHover
            />
            {!isOriginal && (
              <CardHoverInfo
                data={cardHover}
                tipData={tipDataItem}
                profile={profile}
                expiredString={expiredString}
                type={cardHover?.type}
                tvod={cardHover?.tvod}
              />
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default CardHoverItem;
