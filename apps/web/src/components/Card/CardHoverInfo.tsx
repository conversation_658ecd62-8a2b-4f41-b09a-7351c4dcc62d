import React, { useEffect, useMemo, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import CardProgress from '@components/basic/Card/CardProgress';
import TagsOutline from '@components/basic/Tags/TagsOutline';
import { TEXT } from '@constants/text';
import { CONTENT_TYPE, RIBBON_TYPE, TAG_KEY, EL_THEME_CLASS, TVOD } from '@constants/constants';
import { parseRentType, parseTagsData } from '@helpers/common';
import { formatTimeTVodString } from '@services/contentService';
import { useSelector } from 'react-redux';
import dynamic from 'next/dynamic';
import { setStartTimeLiveStream } from '@services/datetimeServices';
import { setLive } from '@models/epgItem';
import classNames from 'classnames';

const Tags = dynamic(import('@components/basic/Tags/Tags'), { ssr: false });
const CardHoverInfo = React.memo(({ data, tipData, expiredString }: any) => {
  const {
    isOriginal,
    isComingSoon,
    isPremium,
    isLiveTv,
    isLiveStream,
    vodSchedule,
    ribbonType,
    dataLiveTV,
    defaultEpisode,
    moreInfo,
    isLive,
    isPremiumDisplay,
    hasPVOD,
    expiredDate,
    startTime,
    isPremiere,
    tvod,
    tVodInfo,
    hasObjectDetection,
    type,
    isPremiumTVod,
    isSvodTvod
  } = data;
  const hasPremiumDisplay = !isEmpty(isPremiumDisplay);
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const isVideoIndexing = hasObjectDetection && !isKid;
  const { text } = tipData || {};
  const { bizInfo } = tVodInfo || {};
  const { day } = vodSchedule || {};
  const titleLiveTv = moreInfo?.programme?.title;
  const newTagData = parseTagsData({ ...data, ...tipData });
  const [arrayLimit, setArrayLimit] = useState(5);
  const { strTimeStandard, benefitType, price, isLiveEvent, isSimulcast } = tvod || {};
  const startTextLive = useMemo(() => {
    if (startTime) {
      if (type === CONTENT_TYPE.EPG || type === CONTENT_TYPE.LIVE_TV) {
        const { startText } = setLive(startTime);
        return startText;
      }
      if (type === CONTENT_TYPE.LIVESTREAM) {
        return setStartTimeLiveStream(startTime, isLive, isPremiere);
      }
    }
    return '';
  }, [startTime, isPremiere, isLive]);

  const remainTimeText = useMemo(() => {
    if (!tvod) return '';

    const { isRented, isExpired } = parseRentType(tvod);
    if (!isRented || isExpired) {
      return '';
    } else {
      return formatTimeTVodString({
        strConfig: expiredString,
        strTime: tvod?.strTimeStandard
      });
    }
  }, [expiredString, tvod]);

  const progress = useMemo(
    () => data?.progress || tipData?.progress,
    [data?.progress, tipData?.progress]
  );
  const progressPercent = useMemo(
    () => data?.progressPercent || tipData?.progressPercent,
    [data?.progressPercent, tipData?.progressPercent]
  );
  const remainText = useMemo(
    () => data?.remainText || tipData?.remainText,
    [data?.remainText, tipData?.remainText]
  );
  const nextWatchingTitle = useMemo(
    () => (progressPercent > 0 || progress === -1 ? text || defaultEpisode?.title : ''),
    [progressPercent, progress, text, defaultEpisode]
  );
  const title = useMemo(() => {
    if ((day && ribbonType === RIBBON_TYPE.COMING_SOON) || isComingSoon) {
      return day;
    }
    if (isLiveTv && !isEmpty(newTagData) && dataLiveTV?.programme?.id) {
      return dataLiveTV?.programme?.title;
    }
    return data?.title;
  }, [isLiveTv, ribbonType, isComingSoon, newTagData, dataLiveTV, data?.title]);

  useEffect(() => {
    if (isPremium || hasPVOD || (!isLiveEvent && !isSimulcast && !!price && benefitType <= 0)) {
      if (isPremium && hasPVOD) return setArrayLimit(3);
      return setArrayLimit(4);
    }
    setArrayLimit(5);
  }, [isPremium, isLiveEvent, isSimulcast, price, benefitType]);

  if (isOriginal) return null;

  return (
    <div className="intro__info w-full px-4 space-y-3.5">
      <div className={classNames('intro__info__wrap')}>
        <div className="intro__info-left space-y-3.5">
          <div className="flex flex-row flex-wrap space-x-[10px] lg:space-x-3 items-center">
            {(isPremiumTVod || isSvodTvod) &&
              !isLiveEvent &&
              !isSimulcast &&
              !!price &&
              (benefitType === TVOD.USER_TYPE.EXPIRED || benefitType === TVOD.USER_TYPE.NONE) && (
                <Tags tagKey={TAG_KEY.PRICE} price={new Intl.NumberFormat('vi-VN').format(price)} />
              )}

            {isPremium && !isLiveTv && !isLiveStream && hasPremiumDisplay && (
              <Tags isPremiumDisplay={isPremiumDisplay} />
            )}
            {hasPVOD && type === CONTENT_TYPE.SEASON && (
              <Tags tagKey={TAG_KEY.WATCH_SOON} title={TEXT.TAG_WATCH_SOON} />
            )}
            {isPremium && isLiveTv && hasPremiumDisplay && (
              <Tags isPremiumDisplay={isPremiumDisplay} tagKey={TAG_KEY.VIP} />
            )}
            {(isLive || (isPremiere && isLive)) && !startTextLive && (
              <Tags title={isPremiere ? TAG_KEY.PREMIERE : ''} tagKey={TAG_KEY.LIVE} />
            )}
            <TagsOutline
              tagArray={newTagData}
              arrayLimit={arrayLimit}
              isVideoIndexing={isVideoIndexing}
            />
            {startTextLive && isLiveStream && (
              <Tags
                iconName="vie-clock-o-rc-medium !text-[.8125rem] lg:!text-[.875rem]"
                description={startTextLive}
                txtClass="text-large-up-14 !font-medium"
                theme={EL_THEME_CLASS.GREEN_SUBTLE}
                isNewIcon
              />
            )}
          </div>
          {expiredDate && (
            <h4 className="title !text-white">{`${TEXT.CONTENT_LASTING}: ${expiredDate}`}</h4>
          )}
          {progress > 0 && (
            <CardProgress progressPercent={progressPercent} remainText={remainText} />
          )}
          {titleLiveTv && isLiveTv && ribbonType === RIBBON_TYPE.LIVE_TV && (
            <h3 className="intro__info__title !text-white font-medium">{titleLiveTv}</h3>
          )}
          {(ribbonType === RIBBON_TYPE.COMING_SOON ||
            ribbonType === RIBBON_TYPE.EPG ||
            isLiveStream ||
            isLive) && <h3 className="intro__info__title !text-white font-medium">{title}</h3>}
          {nextWatchingTitle && (
            <h5 className="card__title text-white !text-[1rem] font-bold">{nextWatchingTitle}</h5>
          )}
        </div>
      </div>
      {remainTimeText && !(startTextLive && isLiveStream && !isPremiere) && (
        <div className="flex items-center">
          <Tags
            description={remainTimeText}
            theme={EL_THEME_CLASS.YELLOW_SUBTLE}
            iconName="vie-clock-o-rc-medium !text-[.8125rem] mb-[2px]"
            isNewIcon
          />
        </div>
      )}
    </div>
  );
});
export default CardHoverInfo;
