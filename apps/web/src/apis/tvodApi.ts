import ConfigApi from '@config/ConfigApi';
import { API_METHOD } from '@constants/constants';
import { PreorderReminderItem } from '@models/subModels';
import AxiosClient from './axiosClient';

class TVodApi {
  static getReminderPreorder(props?: any) {
    const { pastTime, futureTime } = props || {};
    const method = API_METHOD.GET;
    const url = ConfigApi.tvod.reminderPreOder;

    const params: any = {
      api_version: 1,
      time_length_future: futureTime || 24,
      time_length_past: pastTime || 0
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const items = (res?.data?.items || [])
        .map((item: any) => PreorderReminderItem(item))
        .filter((it: any) => it?.remainTime >= 0)
        .sort((a: any, b: any) => a.remainTime - b.remainTime);
      return { items };
    });
  }
}

export default TVodApi;
