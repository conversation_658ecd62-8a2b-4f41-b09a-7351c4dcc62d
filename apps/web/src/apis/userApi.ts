import Moment from 'moment';
import ConfigApi from '@config/ConfigApi';
import {
  convertKeysToSnakeCaseSelective,
  getDeviceId,
  getModelDevice,
  getValueFromStore,
  isValidDate,
  parseConfigParams,
  parseUrlString,
  removeAccessToken,
  saveAccessToken,
  saveAnonymousToken
} from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import UserServices from '@services/userServices';
import TransactionItem from '@models/transactionItem';
import VoucherItem from '@models/voucherItem';
import LoginResponse from '@models/LoginResponse';
import Profile from '@models/Profile';
import UserType from '@models/userType';
import Register from '@models/register';
import UserPackageInfo from '@models/UserPackageInfo';
import CardItem from '@models/CardItem';
import { sortDataRibbon } from '@services/pageServices';
import {
  checkFilterTVod,
  filterTVod,
  parseTimeEventTVod,
  sortDataLive
} from '@services/tvodService';
import { API_METHOD, HTTP_CODE, PAGE_MAX_SIZE, PLATFORM } from '@constants/constants';
import TrackingApp from '@tracking/functions/TrackingApp';
import { DevicesManagement, Referral } from '@models/subModels';
import isEmpty from 'lodash/isEmpty';
import ConfigCookie from '@config/ConfigCookie';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import AxiosClient from './axiosClient';

class UserApi {
  static globalLinkPhoneNumber({ phoneNumber, countryCode, accessToken }: any) {
    const url = ConfigApi.user.global.linkPhoneNumber;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      phone: phoneNumber,
      country_code: countryCode
    };
    return AxiosClient.executeWithCache({ url, method, params, config, accessToken }).then(
      (res) => ({
        ...res,
        data: {
          ...res?.data,
          result: {
            confirmationNo: res?.data?.result?.confirmation_no || '',
            expiresIn: res?.data?.result?.expires_in || 0,
            retryAfterTime: res?.data?.result?.retry_after_time || 0
          }
        }
      })
    );
  }
  static globalShareLoginDevice(code: any) {
    const url = ConfigApi.user.global.shareLoginDevice;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      code
    };
    return AxiosClient.executeWithCache({ url, method, config, params });
  }
  static globalRefreshToken() {
    const url = ConfigApi.user.global.refreshToken;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};
    const params: any = {
      token: accessToken
    };
    const accessTokenRequest = ConfigCookie.load(ConfigCookie.KEY.REFRESH_TOKEN);
    return AxiosClient.executeWithCache({
      url,
      method,
      config,
      accessToken: accessTokenRequest,
      params
    })?.then((res: any) => {
      if (res?.success && res?.data?.result?.access_token) {
        saveAccessToken(
          res?.data?.result?.access_token,
          res?.data?.result?.profile_token,
          res?.data?.result?.refresh_token
        );
      } else {
        removeAccessToken();
      }
      return res;
    });
  }

  static async globalUpdatePassword({ priority, notificationType }: any) {
    const url = ConfigApi.user.global.updatePassword;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      priority,
      notification_type: notificationType
    };
    return AxiosClient.executeWithCache({ url, method, params, config })?.then((res: any) => ({
      ...res,
      data: {
        ...res?.data,
        result: {
          confirmationNo: res?.data?.result?.confirmation_no || '',
          expiresIn: res?.data?.result?.expires_in || 0,
          retryAfterTime: res?.data?.result?.retry_after_time || 0,
          notificationType
        }
      }
    }));
  }

  static globalRestoreAccount({ accessToken, model, deviceId, deviceName, deviceType }: any) {
    const url = ConfigApi.user.global.restoreAccount;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      model,
      device_id: deviceId,
      device_name: deviceName,
      device_type: deviceType
    };
    return AxiosClient.executeWithCache({ url, method, params, config, accessToken }).then(
      (res) => {
        const token = res?.data?.result?.access_token;
        if (token) {
          return {
            ...res,
            data: new LoginResponse(res?.data)
          };
        }
        return {
          ...res,
          data: {
            ...res?.data,
            result: {
              confirmationNo: res?.data?.result?.confirmation_no || '',
              expiresIn: res?.data?.result?.expires_in || 0,
              retryAfterTime: res?.data?.result?.retry_after_time || 0
            }
          }
        };
      }
    );
  }

  static globalForgotPassword({
    userName,
    countryCode,
    model,
    deviceId,
    deviceName,
    deviceType,
    notificationType,
    captchaToken
  }: any) {
    const url = ConfigApi.user.global.forgotPassword;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'X-Recaptcha-Token': captchaToken
      }
    };
    const params: any = {
      username: userName,
      country_code: countryCode,
      model,
      device_id: deviceId,
      device_name: deviceName,
      device_type: deviceType,
      notification_type: notificationType
    };
    return AxiosClient.executeWithCache({ url, method, params, config })?.then((res: any) => ({
      ...res,
      data: {
        ...res?.data,
        result: {
          confirmationNo: res?.data?.result?.confirmation_no || '',
          expiresIn: res?.data?.result?.expires_in || 0,
          retryAfterTime: res?.data?.result?.retry_after_time || 0,
          notificationType: res?.data?.result?.notification_type || ''
        }
      }
    }));
  }

  static loginAnonymous({ deviceId, model }: any) {
    const url = ConfigApi.user.loginAnonymous;
    const method = API_METHOD.POST;
    const params: any = {
      device_id: deviceId || getDeviceId(),
      model: model || getValueFromStore('App.deviceModel')
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      saveAnonymousToken(res?.data?.access_token);
      return {
        ...res,
        data: {
          accessToken: res?.data?.access_token
        }
      };
    });
  }

  static async globalLoginSocial({
    provider,
    token,
    model,
    deviceId,
    deviceName,
    deviceType
  }: any) {
    const url = `${ConfigApi.user.global.loginSocial}/${provider}`;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      token,
      model,
      device_id: deviceId,
      device_name: deviceName,
      device_type: deviceType
    };
    return AxiosClient.executeWithCache({ url, method, params, config })?.then((res: any) => ({
      ...res,
      data: new LoginResponse(res?.data)
    }));
  }

  static async authenticationDeviceLogin({ model, deviceId, deviceName, deviceType }: any) {
    const url = ConfigApi.user.global.authenticationDeviceLogin;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      model,
      device_id: deviceId,
      device_name: deviceName,
      device_type: deviceType
    };
    return AxiosClient.executeWithCache({ url, method, params, config })?.then((res: any) => ({
      ...res,
      data: {
        expiresIn: res?.data?.result?.expires_in || 0,
        code: res?.data?.result?.code,
        interval: res?.data?.result?.interval,
        userCode: res?.data?.result?.user_code
      }
    }));
  }

  static async getAuthenticationDeviceLoginStatus(code: any) {
    const url = `${ConfigApi.user.global.authenticationDeviceLogin}?code=${code}`;
    const method = API_METHOD.GET;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = { code };
    return AxiosClient.executeWithCache({ url, method, params, config })?.then((res: any) => ({
      ...res,
      data: {
        expiresIn: res?.data?.result?.expires_in || 0,
        accessToken: res?.data?.result?.access_token,
        refreshToken: res?.data?.result?.refresh_token,
        code: res?.data?.code
      }
    }));
  }

  static async globalValidateOTP({ confirmationNo, code }: any) {
    const url = ConfigApi.user.global.validateOTP;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      confirmation_no: confirmationNo,
      code
    };
    return AxiosClient.executeWithCache({ url, method, params, config })?.then((res: any) => {
      const success = res?.success && res?.data?.code === 0;
      const message = !success
        ? res?.data?.message || res?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau ít phút'
        : '';
      return { success, message };
    });
  }

  static async globalConfirmOTP({
    confirmationNo,
    code,
    oldPassword,
    password,
    accessToken,
    gender,
    dob
  }: any) {
    const url = ConfigApi.user.global.confirmOTP;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      confirmation_no: confirmationNo,
      password: password || '',
      old_password: oldPassword || '',
      code,
      gender,
      dob
    };
    return AxiosClient.executeWithCache({ url, method, params, config, accessToken }).then(
      (res) => ({
        ...res,
        data: new LoginResponse(res?.data)
      })
    );
  }
  static getLoginMethod() {
    const method = API_METHOD.GET;
    const url = ConfigApi.user.global.login;
    return AxiosClient.executeWithCache({ url, method });
  }

  static async globalLogin({
    userName,
    password,
    countryCode,
    model,
    deviceId,
    deviceName,
    deviceType
  }: any) {
    const url = ConfigApi.user.global.login;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      username: userName,
      password,
      country_code: countryCode || '',
      model,
      device_id: deviceId,
      device_name: deviceName,
      device_type: deviceType
    };
    return AxiosClient.executeWithCache({ url, method, params, config })?.then((res: any) => ({
      ...res,
      data: new LoginResponse(res?.data)
    }));
  }

  static async globalRegister({
    userName,
    countryCode,
    model,
    deviceId,
    deviceName,
    deviceType,
    captchaToken
  }: any) {
    const url = ConfigApi.user.global.register;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'X-Recaptcha-Token': captchaToken
      }
    };
    const params: any = {
      username: userName,
      country_code: countryCode,
      model,
      device_id: deviceId,
      device_name: deviceName,
      device_type: deviceType
    };
    return AxiosClient.executeWithCache({ url, method, params, config })?.then((res: any) => ({
      ...res,
      data: {
        ...res?.data,
        result: {
          confirmationNo: res?.data?.result?.confirmation_no || '',
          expiresIn: res?.data?.result?.expires_in || 0,
          retryAfterTime: res?.data?.result?.retry_after_time || 0
        }
      }
    }));
  }

  static async loginMobile({ phoneNumber, password, deviceName, deviceType }: any) {
    const url = ConfigApi.user.loginMobile;
    const deviceId = getDeviceId();
    const method = API_METHOD.POST;
    const params: any = {
      phone_number: phoneNumber,
      password,
      device_id: deviceId,
      platform: PLATFORM.WEB,
      model: getModelDevice(),
      push_token: '',
      device_name: deviceName,
      device_type: deviceType,
      isMorePlatform: true
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const data = new LoginResponse(res?.data);
      return { ...res, data };
    });
  }
  static setUserTransaction(params: any) {
    const url = ConfigApi.user.userTransaction;
    const method = ConfigApi.METHOD.POST;
    return AxiosClient.executeWithCache({ url, method, params });
  }

  static getUserPackageInfo() {
    const method = API_METHOD.GET;
    const url = ConfigApi.billing.paymentConversion;
    return AxiosClient.executeWithCache({ url, method })?.then((res: any) => {
      const data = new UserPackageInfo(res?.data);
      return { ...res, data };
    });
  }

  static getUserTvod({
    isPreOrder,
    isReminder,
    idTvodData,
    idTvodPreOrderData,
    ribbonOrder,
    ribbonId,
    ribbonName,
    isGlobal
  }: any) {
    const method = API_METHOD.GET;
    const url = ConfigApi.tvod.tvod;
    const params: any = {
      close_to_expiry: isReminder ? 1 : '',
      pre_order: isPreOrder ? 1 : ''
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const items = (res?.data?.items || []).map(
        (item: any, index: any) =>
          new CardItem({ ...item, ribbonOrder, ribbonId, ribbonName, isGlobal }, index)
      );
      let dataReminder = [];
      let dataPreOrder = [];
      let typeSingleTVod;
      let typeSingleTVodPreOder;
      if (isReminder) {
        dataReminder = filterTVod({ data: items });
        if (idTvodData) {
          const arrFilter = checkFilterTVod({ data: dataReminder, dataCheck: idTvodData });
          if (arrFilter?.length > 0) {
            ConfigLocalStorage.set(LocalStorage.ID_TVOD_DATA, JSON.stringify(dataReminder) || '');
            const getDataTVodPreOrder: any =
              ConfigLocalStorage.get(LocalStorage.ID_TVOD_PRE_ORDER_DATA) || 'null';
            dataPreOrder = JSON.parse(getDataTVodPreOrder || '');
          } else dataReminder = [];
        } else {
          ConfigLocalStorage.set(LocalStorage.ID_TVOD_DATA, JSON.stringify(dataReminder) || '');
        }
        sortDataRibbon(dataReminder);
      } else if (isPreOrder) {
        dataPreOrder = filterTVod({ data: items });
        dataPreOrder = dataPreOrder.filter((item: any) => {
          const { isNotAreaReminder } = parseTimeEventTVod({
            startTime: item?.startTime,
            isLive: item?.isLive
          });
          if (!isNotAreaReminder) return item;
        });
        if (idTvodPreOrderData) {
          const arrFilter = checkFilterTVod({ data: dataPreOrder, dataCheck: idTvodPreOrderData });
          if (arrFilter?.length > 0) {
            ConfigLocalStorage.set(
              LocalStorage.ID_TVOD_PRE_ORDER_DATA,
              JSON.stringify(dataPreOrder) || ''
            );
            const getDataTVod: any = ConfigLocalStorage.get(LocalStorage.ID_TVOD_DATA) || null;
            dataReminder = JSON.parse(getDataTVod || '');
          } else dataPreOrder = [];
        } else {
          ConfigLocalStorage.set(
            LocalStorage.ID_TVOD_PRE_ORDER_DATA,
            JSON.stringify(dataPreOrder) || ''
          );
        }
        sortDataLive(dataPreOrder);
      } else sortDataRibbon(items);
      typeSingleTVodPreOder = dataPreOrder?.length === 1;
      typeSingleTVod = dataReminder?.length === 1;
      return {
        ...res,
        data: {
          ...res?.data,
          items,
          dataReminder,
          dataPreOrder,
          typeSingleTVod,
          typeSingleTVodPreOder
        }
      };
    });
  }
  static logout() {
    const method = API_METHOD.POST;
    const url = ConfigApi.user.logout;
    const params: any = {
      device_id: getDeviceId()
    };
    return AxiosClient.executeWithCache({ url, method, params });
  }

  static getProfile({
    isMobile,
    isTablet,
    accessToken,
    profileToken,
    ssr,
    ipAddress,
    deviceModel,
    deviceName,
    deviceType,
    isSmartTvPlatform,
    userAgent,
    origin
  }: any) {
    const url = ConfigApi.user.profile;
    const method = ConfigApi.METHOD.GET;
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    const params: any = {
      model: deviceModel || getModelDevice(),
      device_name: deviceName,
      device_type: deviceType,
      isMorePlatform: true
    };
    return AxiosClient.executeWithCache({
      isMobile,
      isTablet,
      url,
      method,
      params,
      config,
      accessToken,
      profileToken,
      isSmartTvPlatform,
      ssr
    })?.then((res: any) => {
      const data = new Profile({ ...res?.data, httpCode: res.httpCode, accessToken });
      if (data?.httpCode !== HTTP_CODE.OK_200) {
        TrackingApp.getProfileError(data);
      }
      return { ...res, data };
    });
  }
  static updateProfile({
    givenName,
    dob,
    gender,
    allowPush,
    deviceModel,
    deviceName,
    deviceType
  }: any) {
    const url = ConfigApi.user.profile;
    const method = API_METHOD.POST;
    const params: any = {
      given_name: givenName,
      dob,
      gender,
      allow_push: allowPush,
      model: deviceModel || getModelDevice(),
      device_name: deviceName,
      device_type: deviceType
    };
    return AxiosClient.executeWithCache({ url, method, params });
  }

  static forgetPassword(phoneNumber: any) {
    const url = ConfigApi.user.forgetPassword;
    const method = API_METHOD.POST;
    const params: any = {
      phone_number: phoneNumber
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => ({ ...res }));
  }

  static userTrialApp(collection: any) {
    const url = ConfigApi.cm.userTrialApp;
    const method = API_METHOD.POST;
    const params: any = {
      items: JSON.stringify(collection),
      platform: PLATFORM.WEB
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => ({ ...res }));
  }
  static confirmOtpForgetPassword(
    registerSessionId: any,
    otpCode: any,
    password: any,
    confirmPassword: any
  ) {
    const url = ConfigApi.user.confirmOtpForgetPassword;
    const method = API_METHOD.POST;
    const params: any = {
      register_session_id: registerSessionId,
      otp_code: otpCode,
      password,
      confirm_password: confirmPassword
    };
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static checkOtpForgetPassword(registerSessionId: any, otpCode: any, accessToken: any) {
    const url = ConfigApi.user.checkOtpForgetPassword;
    const method = API_METHOD.POST;
    const params: any = {
      register_session_id: registerSessionId,
      otp_code: otpCode
    };
    return AxiosClient.executeWithCache({ url, method, params, accessToken });
  }
  static getOtpUpdatePassword() {
    const url = ConfigApi.user.sendOtpUpdatePassword;
    const method = API_METHOD.POST;
    return AxiosClient.executeWithCache({ url, method });
  }
  static confirmOtpUpdatePassword(sessionId: any, otp: any) {
    const url = ConfigApi.user.confirmOtpUpdatePassword;
    const method = API_METHOD.POST;
    const params: any = {
      session_id: sessionId,
      otp_code: otp
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => res);
  }
  static updatePassword(sessionId: any, oldPassword: any, password: any, confirmPassword: any) {
    const url = ConfigApi.user.updatePassword;
    const method = API_METHOD.POST;
    const params: any = {
      session_id: sessionId,
      old_password: oldPassword,
      password,
      confirm_password: confirmPassword
    };
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static updateEmail({ email, deviceModel, deviceName, deviceType }: any) {
    const url = ConfigApi.user.updateEmail;
    const method = API_METHOD.POST;
    const params: any = {
      email,
      model: deviceModel || getModelDevice(),
      device_name: deviceName,
      device_type: deviceType
    };
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static updateMobile(phoneNumber: any, accessToken?: any) {
    const url = ConfigApi.user.updateMobile;
    const method = API_METHOD.POST;
    const params: any = {
      phone_number: phoneNumber
    };
    return AxiosClient.executeWithCache({ url, method, params, accessToken });
  }
  static updateDob({ dob }: any) {
    const url = ConfigApi.user.updateDob;
    const method = API_METHOD.POST;
    const params: any = {
      dob
    };
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static restoreAccount({ accessToken }: any) {
    const url = ConfigApi.user.restoreAccount;
    const method = API_METHOD.POST;
    return AxiosClient.executeWithCache({ url, method, accessToken });
  }
  static confirmRestoreOtpAccount({ accessToken, sessionId, optCode }: any) {
    const url = ConfigApi.user.confirmRestoreOtpAccount;
    const method = API_METHOD.POST;
    const params: any = {
      session_id: sessionId,
      otp_code: optCode
    };
    return AxiosClient.executeWithCache({ url, method, params, accessToken });
  }
  static confirmUpdateMobile(
    registerSessionId: any,
    otpCode: any,
    password: any,
    confirmPassword: any,
    accessToken?: any
  ) {
    const url = ConfigApi.user.confirmUpdateMobile;
    const method = API_METHOD.POST;
    const params: any = {
      register_session_id: registerSessionId,
      otp_code: otpCode,
      password,
      confirm_password: confirmPassword
    };
    return AxiosClient.executeWithCache({ url, method, params, accessToken });
  }
  static getUserNotifyComingSoon(list_id: any) {
    let url = parseUrlString(ConfigApi.user.userNotifyComingSoon);
    const method = API_METHOD.POST;
    const params: any = {
      list_id
    };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    return AxiosClient.executeWithCache({ url, method, params, config });
  }
  static subscribeComingSoon({ contentId, startTime, contentType }: any) {
    const method = API_METHOD.POST;
    let url = parseUrlString(ConfigApi.user.subscribeComingSoon);
    const params: any = {
      content_id: contentId,
      start_time: startTime,
      content_type: contentType
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => res);
  }
  static unsubscribeComingSoon({ contentId }: any) {
    const method = API_METHOD.POST;
    let url = parseUrlString(ConfigApi.user.unsubscribeComingSoon);
    const params: any = { content_id: contentId };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => res);
  }
  static updateInvoiceInfo(invoiceInfoUpdate: any) {
    const method = API_METHOD.PUT;
    const url = ConfigApi.user.updateInvoiceInfo;
    const params: any = convertKeysToSnakeCaseSelective(invoiceInfoUpdate);
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => res);
  }

  static get profile() {
    return {
      getProfile: this.getProfile,
      updateProfile: this.updateProfile,
      forgetPassword: this.forgetPassword,
      confirmOtpForgetPassword: this.confirmOtpForgetPassword,
      updatePassword: this.updatePassword,
      updateEmail: this.updateEmail,
      updateMobile: this.updateMobile,
      confirmUpdateMobile: this.confirmUpdateMobile,
      updateDob: this.updateDob,
      restoreAccount: this.restoreAccount,
      confirmRestoreOtpAccount: this.confirmRestoreOtpAccount,
      updateInvoiceInfo: this.updateInvoiceInfo
    };
  }

  static registerMobile({ phoneNumber, givenName, password, deviceName, deviceType }: any) {
    const url = ConfigApi.user.registerMobile;
    const method = API_METHOD.POST;
    const params: any = {
      phone_number: phoneNumber,
      password,
      given_name: givenName,
      device_id: getDeviceId(),
      platform: PLATFORM.WEB,
      model: getModelDevice(),
      push_token: '',
      device_name: deviceName,
      device_type: deviceType,
      isMorePlatform: true
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const data = new Register(res?.data);
      return { ...res, data };
    });
  }

  static confirmOtpRegisterMobile(registerSessionId: any, otpCode: any) {
    const url = ConfigApi.user.confirmOtpRegisterMobile;
    const method = API_METHOD.POST;
    const params: any = {
      register_session_id: registerSessionId,
      otp_code: otpCode
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const data = { message: res?.data?.message, accessToken: res?.data?.access_token };
      return { ...res, data };
    });
  }

  static resendOtpCodeRegister(phoneNumber: any) {
    const url = ConfigApi.user.resendOtpCodeRegister;
    const method = API_METHOD.POST;
    const params: any = {
      phone_number: phoneNumber,
      device_id: getDeviceId(),
      model: getModelDevice()
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const data = {
        message: res?.data?.message,
        registerSessionId: res?.data?.register_session_id
      };
      return { ...res, data };
    });
  }

  static getUserReport() {
    const url = ConfigApi.cm.reportType;
    const storedData = ConfigLocalStorage.get(LocalStorage.REPORT_VIDEO);
    // Make sure we only parse valid JSON strings
    const userReport = typeof storedData === 'string' ? JSON.parse(storedData) : null;
    if (!userReport || !userReport?.items?.length) {
      const method = API_METHOD.GET;
      const params: any = {};
      return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
        const reportData = res?.data;
        ConfigLocalStorage.set(LocalStorage.REPORT_VIDEO, JSON.stringify(reportData));
        return res;
      });
    }
    // Wrap the userReport in a resolved promise to ensure consistent return type
    return Promise.resolve(userReport);
  }
  static postUserReport(
    content_id: any,
    report_ids: any,
    message: any,
    video_profile: any,
    audio_profile: any,
    subtitle: any,
    os_version: any,
    time_seeker: any,
    entity_type: any
  ) {
    const method = ConfigApi.METHOD.POST;
    const url = parseUrlString(ConfigApi.cm.postUserReport, 'contentId', content_id);
    let params = {
      report_ids,
      message,
      video_profile: video_profile || '',
      audio_profile: audio_profile || '',
      subtitle: subtitle || '',
      os_version: os_version || '',
      time_seeker: time_seeker || 0,
      entity_type: entity_type || 0
    };

    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    });
  }
  static trackingWatch({
    contentId,
    contentName,
    contentType,
    usi,
    data,
    playTrial,
    videoCodec
  }: any) {
    const url = `${ConfigApi.cm.userProgressing}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      content_id: contentId,
      play_trial: playTrial,
      content_name: contentName,
      content_type: contentType,
      video_codec: videoCodec || 'avc1',
      data,
      usi
    };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    return AxiosClient.executeWithCache({ url, method, params, config });
  }
  static verifyEmail(register_session_id: any, otp: any) {
    const url = ConfigApi.cm.verifyEmail;
    const method = ConfigApi.METHOD.POST;
    const params: any = { register_session_id, otp_code: otp };
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static userFeedBack() {
    const url = ConfigApi.cm.userFeedBack;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({ url, method });
  }

  static checkVoucherExist(voucherCode: any) {
    const url = ConfigApi.billing.voucherCodeCheck;
    const method = ConfigApi.METHOD.POST;
    const params: any = { voucher_code: voucherCode };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const data = new VoucherItem(res?.data);
      return { ...res, data };
    });
  }

  static activeVoucher(voucherCode: any) {
    const url = ConfigApi.billing.voucherCode;
    const method = ConfigApi.METHOD.POST;
    const params: any = { voucher_code: voucherCode };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const data = {
        errorCode: res?.data?.error_code || res?.data?.error,
        errorMessage: res?.data?.message,
        errorString: res?.data?.error_string,
        promotionData: res?.data?.promotion_data,
        success: res?.data?.success,
        expiredDate:
          res?.data?.expired_date && isValidDate(new Date(res?.data?.expired_date))
            ? Moment(new Date(res?.data?.expired_date)).format('DD/MM/YYYY')
            : ''
      };
      return { ...res, data };
    });
  }

  static getTransactions({ page, pageSize }: any) {
    const url = ConfigApi.billing.getUserTransactions;
    const method = ConfigApi.METHOD.GET;
    const params: any = {
      page_index: page,
      api_version: 1,
      page_size: pageSize
    };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const items = (res?.data?.result?.items || []).map((item: any) => new TransactionItem(item));
      const tableData = !isEmpty(items) && UserServices.parseTransactionDetailTable(items);
      const meta = {
        pageTotal: res?.data?.result?.page_total,
        pageSize,
        page,
        nextPage: res?.data?.result?.has_next_page,
        pageIndex: res?.data?.result?.page_index
      };
      return { data: { items, tableData, metadata: meta } };
    });
  }
  static getPurchased() {
    const url = ConfigApi.billing.getPurchased;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({ url, method })?.then((res: any) => res);
  }

  static getConfig({ key, ssr }: any) {
    const url = `${ConfigApi.user.getConfig}?key=${key}`;
    const method = ConfigApi.METHOD.GET;
    const platform = PLATFORM.WEB;
    const params: any = { platform };
    return AxiosClient.executeWithCache({ url, method, params, isMobile: false, ssr }).then(
      (res) => {
        let value = res?.data?.data?.value;
        let type = res?.data?.data?.type;
        if (type === 'json') {
          try {
            value = JSON.parse(value);
          } catch (e) {
            console.log('getConfig', e);
          }
        }
        return value;
      }
    );
  }

  static getUserType(accessToken: any) {
    const url = ConfigApi.user.getUserType;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({ url, method, accessToken })?.then((res: any) => {
      const data = new UserType(res?.data);
      return { ...res, data };
    });
  }

  static getReferralProg() {
    const url = ConfigApi.referral;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({ url, method })?.then((res: any) => {
      const data = Referral(res?.data?.result);
      return data;
    });
  }
  static personalAudioSubTitle(params: any) {
    const url = ConfigApi.user.personalAudioSubTitle;
    const method = ConfigApi.METHOD.POST;
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      if (res?.httpCode === HTTP_CODE.EXPIRE) {
        return this.globalRefreshToken().then((refreshRes) => {
          if (refreshRes?.success) return AxiosClient.executeWithCache({ url, method, params });
          return res;
        });
      }
      return res;
    });
  }

  static getDevicesManagement() {
    const url = ConfigApi.user.getDevicesManagement;
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({ url, method })?.then((res: any) =>
      DevicesManagement(res?.data)
    );
  }
  static disabledDevicesManagement(result: any) {
    const url = ConfigApi.user.disabledDevicesManagement;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      devices: result
    };
    const method = API_METHOD.POST;
    return AxiosClient.executeWithCache({
      url,
      config,
      method,
      params
    });
  }

  static getSegmentedUser() {
    const url = ConfigApi.user.getSegmentedUser;
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      if (res?.success) {
        const result = res?.data?.result?.data;
        const data = {
          segmentCode: result?.segment_code,
          promotionCode: result?.promotion_code,
          packageGroupId: result?.package_group_id,
          packageId: result?.package_id,
          suggestRibbonId: result?.suggest_ribbon_id,
          userId: result?.user_id,
          userName: result?.user_name,
          isPermission: res?.data?.result?.permission === 1,
          full: {
            message: result?.full?.message,
            imageUrl: result?.full?.image,
            imageMWebUrl: result?.full?.imagemweb || result?.full?.image_mweb,
            dismiss: result?.full?.dismiss,
            action: result?.full?.action,
            linkExternal: result?.full?.link_external,
            kvUrl: result?.full?.kv_url
          }
        };
        return data;
      }
      return null;
    });
  }

  static async getInfoLoyalty({ userId }: any) {
    const url = parseUrlString(ConfigApi.loyalty.info, 'userId', userId);
    const method = API_METHOD.GET;
    const params: any = { userId };
    const res = await AxiosClient.executeWithCache({ url, method, params });
    return res?.data;
  }

  static async getTierBenefit() {
    const url = ConfigApi.loyalty.tierBenefit;
    const method = API_METHOD.GET;
    const res = await AxiosClient.executeWithCache({ url, method });
    return res?.data;
  }

  static async getEarningActivity({ userId, pageSize }: any) {
    let url = parseUrlString(ConfigApi.loyalty.earningActivityHistory, 'userId', userId, true);
    url = parseUrlString(url, 'userId', userId, true);
    url = parseUrlString(url, 'pageSize', pageSize || PAGE_MAX_SIZE, true);

    const method = API_METHOD.GET;
    const params: any = { userId };
    const res = await AxiosClient.executeWithCache({ url, method, params });
    return res?.data;
  }

  static async getUsedPointHistory({ userId, pageSize }: any) {
    let url = parseUrlString(ConfigApi.loyalty.usedPointHistory, 'userId', userId);
    url = parseUrlString(url, 'UserId', userId);
    url = parseUrlString(url, 'pageSize', pageSize || PAGE_MAX_SIZE, true);

    const method = API_METHOD.GET;
    const params: any = { userId };
    const res = await AxiosClient.executeWithCache({ url, method, params });
    return res?.data;
  }

  static async getActivities({ pageSize, statuses }: any) {
    let url = parseUrlString(
      ConfigApi.loyalty.earningActivities,
      'statuses',
      statuses || true,
      true
    );
    url = parseUrlString(url, 'pageSize', pageSize || 9, true);

    const method = API_METHOD.GET;
    const res = await AxiosClient.executeWithCache({ url, method });
    return res?.data;
  }

  static async getVouchers({
    userId,
    categoryIds,
    subCategoryIds,
    voucherPageSize,
    categoryPageSize
  }: any) {
    let url = parseUrlString(ConfigApi.loyalty.vouchers, 'userId', userId, true);
    url = parseUrlString(url, 'categoryIds', categoryIds, true);
    url = parseUrlString(url, 'subCategoryIds', subCategoryIds, true);
    url = parseUrlString(url, 'voucherPageSize', voucherPageSize, true);
    url = parseUrlString(url, 'categoryPageSize', categoryPageSize || PAGE_MAX_SIZE, true);

    const method = API_METHOD.GET;
    const res = await AxiosClient.executeWithCache({ url, method });
    return res?.data;
  }

  static async redeemVoucher({ userId, voucherId, codeDeliveryChannel, phoneNumber, email }: any) {
    const url = parseUrlString(ConfigApi.loyalty.redeemVoucher, 'userId', userId);
    const params: any = { userId, voucherId, codeDeliveryChannel, phoneNumber, email };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const method = API_METHOD.POST;
    const res = await AxiosClient.executeWithCache({ url, method, params, config });
    return res?.data;
  }

  static async updateStatusRedeemCode({ userId, voucherCode }: any) {
    const url = parseUrlString(ConfigApi.loyalty.updateStatusRedeemCode, 'userId', userId);
    const params: any = { userId, voucherCode };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const method = API_METHOD.PUT;
    const res = await AxiosClient.executeWithCache({ url, method, params, config });
    return res?.data;
  }

  static async postDeviceToken({ userId, token, deviceType, deviceId }: any) {
    const url = ConfigApi.loyalty.deviceToken;
    const params: any = { userId, token, deviceType, deviceId };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const method = API_METHOD.POST;
    const res = await AxiosClient.executeWithCache({ url, method, params, config });
    return res?.data;
  }

  static async deleteDeviceToken({ userId, deviceId }: any) {
    const url = ConfigApi.loyalty.deviceToken;
    const params: any = { userId, deviceId };

    const method = API_METHOD.DELETE;
    const res = await AxiosClient.executeWithCache({ url, method, params });
    return res?.data;
  }

  static async linkWithSocial({ provider, token }: any) {
    const url = `${ConfigApi.user.socialLinked}/${provider}`;
    const method = API_METHOD.PUT;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      token
    };
    const res = await AxiosClient.executeWithCache({ url, method, params, config });
    return res?.data;
  }

  static async unlinkWithSocial({ provider }: any) {
    const url = `${ConfigApi.user.socialLinked}/${provider}`;
    const method = API_METHOD.DELETE;
    const res = await AxiosClient.executeWithCache({ url, method });
    return res?.data;
  }
  static async tooltipShowTracking(key: any) {
    const url = ConfigApi.user.tooltipShowTracking;
    const method = API_METHOD.GET;
    const params: any = {
      key
    };
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static async tooltipSaveTracking(key: any) {
    const url = ConfigApi.user.tooltipShowTracking;
    const method = API_METHOD.POST;
    const params: any = {
      key
    };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    return AxiosClient.executeWithCache({ url, method, params, config });
  }
  static async getTriggerAfterRegister() {
    const url = ConfigApi.user.triggerAfterRegister;
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({ url, method });
  }
}

// Export component
export default UserApi;
