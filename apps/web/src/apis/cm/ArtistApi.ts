import ConfigApi from '@config/ConfigApi';
import { getALTSEOImage, parseConfigParams } from '@helpers/common';
import CardItem from '@models/CardItem';
import { SeoItem } from '@models/subModels';
import AxiosClient from '../axiosClient';

export default class ArtistApi {
  static getInfoArtist({ slug, ssr, ipAddress, userAgent, origin }: any) {
    const url = `${ConfigApi.cm.infoArtist}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = { people_slug: slug };
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({ url, method, params, ssr, config })
      ?.then((res: any) => res)
      .catch((err: any) => ({ ...err?.response?.data, data: null }));
  }

  static getContentArtist({
    slug,
    page,
    limit,
    sort,
    accessToken,
    ssr,
    ipAddress,
    userAgent,
    isGlobal,
    origin
  }: any) {
    page = page || 0;
    limit = limit || 30;
    sort = sort || 2;
    const url = `${ConfigApi.cm.contentArtist}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      people_slug: slug,
      page,
      limit,
      sort
    };
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({ url, method, params, accessToken, ssr, config })?.then(
      (res: any) => {
        let newItems = (res?.data?.items || []).map(
          (item: any, index: any) => new CardItem({ ...item, isGlobal }, index)
        );

        if (newItems) {
          newItems = getALTSEOImage(newItems);
        }

        const seo = SeoItem(res?.data?.seo);
        return { ...res, data: { ...res?.data, seo, items: newItems } };
      }
    );
  }
  static getArtistRelated({ slug, page, limit }: any) {
    page = typeof Number(page) === 'number' ? Number(page) : 0;
    limit = typeof Number(limit) === 'number' ? Number(limit) : 5;
    const url = `${ConfigApi.cm.artistRelated}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      people_slug: slug,
      page,
      limit
    };
    return AxiosClient.executeWithCache({ url, method, params });
  }
}
