import ConfigApi from '@config/ConfigApi';
import { API_METHOD } from '@constants/constants';
import axios from 'axios';
import CardItem from '@models/CardItem';
import { SeoItem } from '@models/subModels';
import { parseConfigParams } from '@helpers/common';
import AxiosClient from '../axiosClient';

export default class SearchApi {
  static getTrendKeywords({ page, limit, isGlobal }: any) {
    const url = `${ConfigApi.cm.searchNewTrendKeyWords}`;
    const method = API_METHOD.GET;
    const params: any = { limit: limit || 10, page: page || 0 };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const newItems = (res?.data?.items || []).map(
        (item: any, index: any) => new CardItem({ ...item, isSearchPage: true, isGlobal }, index)
      );
      const seo = SeoItem(res?.data?.seo);
      return { ...res, data: { ...res?.data, seo, items: newItems } };
    });
  }

  static getSearchNewForyou({ page, limit, isGlobal }: any) {
    const url = `${ConfigApi.cm.searchNewForyou}`;
    const method = API_METHOD.GET;
    const params: any = { limit: limit || 10, page: page || 0 };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const newItems = (res?.data?.items || []).map(
        (item: any, index: any) => new CardItem({ ...item, isSearchPage: true, isGlobal }, index)
      );
      const seo = SeoItem(res?.data?.seo);
      return { ...res, data: { ...res?.data, seo, items: newItems } };
    });
  }

  static delSearchHistory() {
    const url = `${ConfigApi.cm.searchHistory}`;
    return axios.delete(url);
  }
  static postSearchHistory({ keyword, id, type, position, request_id }: any) {
    const url = `${ConfigApi.cm.searchHistory}?position=${position}&request_id=${request_id}`;
    let dataSend = {};
    if (type === 'artist') {
      dataSend = {
        keyword,
        artist_ids: `["${id}"]`
      };
    } else {
      dataSend = {
        keyword,
        content_ids: `["${id}"]`
      };
    }
    const method = API_METHOD.POST;
    return AxiosClient.executeWithCache({ url, method, params: dataSend });
  }

  static getSearchContent({
    keyword,
    page,
    limit,
    tags,
    accessToken,
    ssr,
    ipAddress,
    userAgent,
    isGlobal,
    origin
  }: any) {
    let url = '';
    if (tags) {
      url = `${ConfigApi.cm.searchContent}?keyword=${keyword}&version=2.0&tags=${tags}`;
    } else {
      url = `${ConfigApi.cm.searchContent}?keyword=${keyword}&version=2.0`;
    }
    const method = API_METHOD.GET;
    const params: any = { page: page || 0, limit: limit || 60 };
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({ url, method, params, accessToken, ssr, config }).then(
      (res) => {
        const newItems = (res?.data?.items || []).map(
          (item: any, index: any) => new CardItem({ ...item, isSearchPage: true, isGlobal }, index)
        );
        const seo = SeoItem(res?.data?.seo);
        return { ...res, data: { ...res?.data, seo, items: newItems } };
      }
    );
  }
  static getSearchSuggest({ keyword }: any) {
    const url = `${ConfigApi.cm.searchSuggest}?keyword=${keyword}&version=2.0&sort=2&page=0`;
    const method = API_METHOD.GET;
    const params: any = {};
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static getSearchHistory() {
    const url = `${ConfigApi.cm.searchHistory}?version=2.0&limit=10`;
    const method = API_METHOD.GET;
    const params: any = {};
    return AxiosClient.executeWithCache({ url, method, params });
  }
}
