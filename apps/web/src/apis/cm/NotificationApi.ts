import ConfigApi from '@config/ConfigApi';
import { API_METHOD } from '@constants/constants';
import MessageItem from '@models/MessageItem';
import AxiosClient from '../axiosClient';
import { parseUrlString } from '@helpers/common';

export default class NotificationApi {
  static getListNotification({ limit = 10, page = 0 }) {
    const store = window && typeof window !== 'undefined' && window.__NEXT_REDUX_STORE__;
    const state = store.getState();
    const isLoyaltyNoti = state?.App?.webConfig?.featureFlag?.isOnLoyalty || false;
    const url = `${
      isLoyaltyNoti ? ConfigApi.cm.notificationV2 : ConfigApi.cm.notification
    }?unread=false&limit=${limit}&page=${page}`;
    const method = API_METHOD.GET;
    const params: any = {};
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const notifications = (res?.data?.result?.notifications || []).map(
        (item: any) => new MessageItem(item)
      );
      return { ...res, data: { ...res?.data?.result, notifications } };
    });
  }
  static getCountNotification() {
    const store = window && typeof window !== 'undefined' && window.__NEXT_REDUX_STORE__;
    const state = store.getState();
    const isLoyaltyNoti = state?.App?.webConfig?.featureFlag?.isOnLoyalty || false;
    const url = isLoyaltyNoti ? ConfigApi.cm.countNotificationV2 : ConfigApi.cm.countNotification;
    const method = API_METHOD.GET;
    const params: any = {};
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static readNotification({ notifyId, action }: any) {
    const url = ConfigApi.cm.notification;
    let params: any = { action };
    if (notifyId) {
      params.noti_id = notifyId;
    }
    const method = API_METHOD.PUT;
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static trackingNotification(message_id: any) {
    const url = `${ConfigApi.cm.trackingNotification}?is_message=true`;
    const method = API_METHOD.POST;
    const params: any = { message_id };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    return AxiosClient.executeWithCache({ url, method, params, config });
  }

  static checkPolicyConfirmation(id: any): Promise<any> {
    const url = parseUrlString(ConfigApi.cm.checkPolicyConfirmation, 'id', id);
    const method = API_METHOD.GET;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    return AxiosClient.executeWithCache({ url, method, config })?.then((res: any) => {
      const isPolicyDefined = res?.data?.result?.status === 1;
      return { ...res, isPolicyDefined };
    });
  }

  static getDetailPolicyAnnounce(id: any): Promise<any> {
    const url = parseUrlString(ConfigApi.cm.getPolicyAnnounce, 'id', id);
    const method = API_METHOD.GET;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    return AxiosClient.executeWithCache({ url, method, config })?.then((res: any) => {
      return { ...res };
    });
  }

  static confirmDetailPolicyAnnounce({ entityId, notifyId, status }: any): Promise<any> {
    const url = parseUrlString(ConfigApi.cm.setPolicyAnnounce, 'id', entityId);
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      noti_id: notifyId || '',
      status
    };
    return AxiosClient.executeWithCache({ url, method, config, params })?.then((res: any) => {
      return { ...res };
    });
  }
}
