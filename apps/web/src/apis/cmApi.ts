import ConfigApi from '@config/ConfigApi';
import { parseConfigParams } from '@helpers/common';
import { getActiveMenuBySlug, parseMenuData } from '@services/menuServices';
import { HTTP_CODE } from '@constants/constants';
import AxiosClient from './axiosClient';
import { ACTION_TYPE, createAction } from '@actions/actionType';

class CMApi {
  static getMenu({
    slug,
    accessToken,
    profileToken,
    isMobile,
    ipAddress,
    ssr,
    userAgent,
    origin
  }: any) {
    const url = `${ConfigApi.cm.menu}`;
    const method = ConfigApi.METHOD.GET;
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      method,
      url,
      accessToken,
      profileToken,
      isMobile,
      ssr,
      config
    })?.then((res: any) => {
      const menuList = parseMenuData(res?.data?.items);
      const { activeMenu, activeSubMenu, subHeader } = getActiveMenuBySlug({ menuList, slug });
      return {
        activeMenu,
        activeSubMenu,
        menuList,
        subHeader,
        isUnderConstruction: res?.httpCode === HTTP_CODE.UNDER_CONSTRUCTION,
        httpCode: res?.httpCode
      };
    });
  }

  static geoCheck({ ipAddress, ssr, userAgent, globalHost, dispatch }: any) {
    const url = `${ConfigApi.cm.geoCheck}`;
    const method = ConfigApi.METHOD.GET;
    const config = parseConfigParams({ ipAddress, userAgent });
    return AxiosClient.executeWithCache({ method, url, ssr, config })?.then((res: any) => {
      if (res?.httpCode === HTTP_CODE.ERROR_555) {
        return dispatch(createAction(ACTION_TYPE.GEO_CHECK, { notAvailable: true }));
      }
      return {
        ...res,
        data: { ...res?.data, isGlobal: res?.data?.geo_country !== 'VN', globalHost }
        // data: { ...res?.data, isGlobal: true, globalHost: true }
      };
    });
  }

  static getOutstreamAd({ ads_format }: any) {
    const url = `${ConfigApi.cm.getOutstreamAds}`;
    const method = ConfigApi.METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8'
      }
    };
    return AxiosClient.executeWithCache({ method, url, params: { ads_format }, config }).then(
      (res) => res?.data
    );
  }
}

// Export component
export default CMApi;
