import ConfigApi from '@config/ConfigApi';
import { API_METHOD } from '@constants/constants';
import { parseUrlString, setUrlParams } from '@helpers/common';
import AxiosClient from './axiosClient';

class ResultVotingApi {
  static getListRounds() {
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({
      url: ConfigApi.game.voting.listRounds,
      method
    })?.then((res: any) => res.data);
  }

  static getListEpisodesOfRound({ campaignId }: any) {
    const method = API_METHOD.GET;
    const url = parseUrlString(ConfigApi.game.voting.listEpisodesOfRound, 'campaignId', campaignId);
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => res.data);
  }

  static getDetailEpisodeOfFirstRound({ questionId }: any) {
    const method = API_METHOD.GET;
    const url = parseUrlString(
      ConfigApi.game.voting.detailEpisodeOfFirstRound,
      'questionId',
      questionId
    );
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => res.data);
  }

  static getDetailEpisodeOfOtherRound({ campaignId }: any) {
    const method = API_METHOD.GET;
    const url = parseUrlString(
      ConfigApi.game.voting.detailEpisodeOfOtherRound,
      'campaignId',
      campaignId
    );
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => res.data);
  }

  static getListRatingOfEpisode({ questionId }: any) {
    const method = API_METHOD.GET;
    const url = parseUrlString(ConfigApi.game.voting.listRatingOfEpisode, 'questionId', questionId);
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => res.data);
  }

  static getFinalResult({ questionId }: any) {
    const method = API_METHOD.GET;
    const url = parseUrlString(ConfigApi.game.voting.finalResult, 'questionId', questionId);
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => res.data);
  }

  static getListCampaignss() {
    const method = API_METHOD.GET;
    const url = parseUrlString(ConfigApi.game.quiz.listEpisodes);
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => res.data);
  }

  static getListWinnersOfCampaign({ contentId, page = 0, limit = 50 }: any) {
    const method = API_METHOD.GET;
    const url = setUrlParams(
      parseUrlString(ConfigApi.game.quiz.listWinnersOfEpisode, 'contentId', contentId),
      {
        page,
        limit
      }
    );
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => res.data);
  }
}

export default ResultVotingApi;
