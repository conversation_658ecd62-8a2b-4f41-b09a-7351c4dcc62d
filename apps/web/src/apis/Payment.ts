import ConfigApi from '@config/ConfigApi';
import AxiosClient from '@apis/axiosClient';
import Moment from 'moment';
import { mobileCheck, parseUrlString, replaceKey } from '@helpers/common';
import { CONTENT_TYPE, ERROR_CODE, PAYMENT_TYPE, PLATFORM, UTM_SOURCE } from '@constants/constants';
import {
  AsiaPayTransaction,
  BillingPermission,
  MocaTransaction,
  MomoTransaction,
  NapasTransaction,
  PackageItem,
  PromotionData,
  PVodInfo,
  PVodOffer,
  ShopeePayTransaction,
  Transaction,
  TVodInfo,
  TVodOffer,
  ViettelPayTransaction,
  VNPayTransaction,
  ZaloPayTransaction
} from '@models/subModels';
import { parseMethodId } from '@services/paymentServices';
import { parseTVodIdType } from '@services/contentService';

class PaymentApi {
  static setRecurring({ sub_id, recurring }: any) {
    const url = ConfigApi.billing.recurring;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      sub_id,
      recurring
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    });
  }
  static createZaloPayTransaction(packageId: any, giftCode: any, inApp: any) {
    const url = ConfigApi.billing.createTransactionZaloPay;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      package_id: packageId,
      platform: mobileCheck() ? 'mobile_web' : 'web',
      promotion_code: giftCode || '',
      utm_source: inApp ? UTM_SOURCE.ZALO_PAY : ''
    };

    const config = {
      timeout: 20000
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    });
  }

  static checkZaloPayTransaction({ zlOrderId }: any) {
    const url = ConfigApi.billing.checkZaloPayTransaction + zlOrderId;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      const transactionResult = Transaction(res);
      return transactionResult;
    });
  }
  static checkLinkZaloPayTransaction() {
    const url =
      ConfigApi.billing.checkLinkZaloPayTransaction + Math.floor(Math.random() * Math.floor(99999));
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    });
  }
  static getLinkShopeePayTransaction({ paymentMethod, paymentService, returnUrl }: any) {
    const url = ConfigApi.billing.v2.getLinkShopeePayTransaction;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      payment_method: paymentMethod,
      payment_service: paymentService,
      return_url: returnUrl
    };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    });
  }
  static linkZaloPayTransaction({
    callbackLink,
    package_id,
    promotion_code,
    confirm,
    inApp,
    utm_source
  }: any) {
    const url = ConfigApi.billing.linkZaLoPay;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      platform: mobileCheck() ? 'mobile_web' : 'web',
      package_id,
      confirm,
      promotion_code,
      redirect_deep_url: inApp ? '' : encodeURIComponent(callbackLink),
      utm_source
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    });
  }

  static chargeZaloPay({ packageId, inApp, promotionCode, utmSource, trackingId }: any) {
    const url = ConfigApi.billing.chargeZaloPay;
    const method = ConfigApi.METHOD.POST;
    const utmS = inApp ? UTM_SOURCE.ZALO_PAY : utmSource || '';

    const params: any = {
      package_id: packageId,
      promotion_code: promotionCode || '',
      platform: mobileCheck() ? 'mobile_web' : 'web'
    };
    const config = {
      timeout: 20000
    };
    if (utmS) params.utm_source = utmS;
    if (trackingId && utmSource) params.tracking_id = trackingId;
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    })?.then((res: any) => ({
      ...res,
      data: Transaction(res)
    }));
  }

  static checkMocaTransaction({ mcOrderId, tnxId }: any) {
    const url = `${ConfigApi.billing.checkMocaTransaction + mcOrderId}?tnxId=${tnxId}`;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      const transactionResult = Transaction(res);
      return transactionResult;
    });
  }

  static getVnPayList() {
    const url = ConfigApi.billing.getVnPayList;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      const vnPayList = (res?.data?.items || []).map((item: any, index: any) => ({
        ...item,
        id: index
      }));
      return vnPayList;
    });
  }

  static getConfig({ userProfile, packageId }: any) {
    let key = `sms_payment_${packageId}`;
    if (!userProfile?.id) key += '_no_info';
    const url = `${ConfigApi.user.getConfig}?key=${key}`;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      const data = { ...res?.data?.data };

      if (userProfile?.id) {
        let type = 'email';
        let user = userProfile?.email;
        if (userProfile?.mobile) {
          type = 'số điện thoại';
          user = userProfile.mobile;
        }
        const value = replaceKey(replaceKey(data?.value, 'type', type), 'user', user);
        data.value = value;
      }
      return data;
    });
  }
  static createMoMoTransaction({ packageId, returnUrl }: any) {
    const url = ConfigApi.billing.createMomoTransaction;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      package_id: packageId,
      returnUrl
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    });
  }
  static checkMoMoTransaction({ orderId }: any) {
    const url = ConfigApi.billing.checkMomoTransaction;
    const method = ConfigApi.METHOD.GET;
    const params: any = { txn_ref: orderId };
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    });
  }
  static createVnPayTransaction({ packageId, url_return, order_info, bankCode, giftCode }: any) {
    const url = ConfigApi.billing.createVnPayTransaction;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      package_id: packageId,
      order_info,
      url_return,
      bank_code: bankCode,
      promotion_code: giftCode || ''
    };
    const config = {
      timeout: 20000
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    });
  }

  static checkVnPayTransaction({ orderId }: any) {
    const url = ConfigApi.billing.checkVnPayTransaction;
    const method = ConfigApi.METHOD.GET;
    const params: any = { txn_ref: orderId };
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    })?.then((res: any) => {
      const transactionResult = Transaction(res);
      return transactionResult;
    });
  }
  static createAsianPayTransaction({ packageId, giftCode }: any) {
    const url = ConfigApi.billing.createAsianPayTransaction;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      package_id: packageId,
      promotion_code: giftCode || ''
    };
    const config = {
      timeout: 20000
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    });
  }

  static getAsianPayChannel() {
    const url = ConfigApi.billing.getAsianPayChannel;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      const data = res?.data?.items?.[0];
      const config = JSON.parse(data?.Config || '{}');
      return {
        ...data,
        config
      };
    });
  }

  static checkAsiaPayTransaction({ orderId }: any) {
    const url = ConfigApi.billing.checkAsiaPayTransaction;
    const method = ConfigApi.METHOD.GET;
    const params: any = { txn_ref: orderId };
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    })?.then((res: any) => {
      const transactionResult = Transaction(res);
      return transactionResult;
    });
  }

  static getPackages({ isInAppZalo }: any) {
    let url = ConfigApi.billing.packages;
    if (isInAppZalo) url += '?sub_platform=zalopay_inapp';
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => (res?.data?.items || []).map((item: any) => PackageItem(item)));
  }

  static promotionCode({ packageId, promotionCode, selectedMethod, userId }: any) {
    let url = parseUrlString(ConfigApi.cm.promotioncode_detail.promotioncode, 'userId', userId);
    url = parseUrlString(url, 'promotionCode', promotionCode);
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      payment_method: parseMethodId((selectedMethod?.id || '').toLowerCase()),
      package_id: packageId,
      platform: mobileCheck() ? PLATFORM.MOBILE_WEB : PLATFORM.WEB
    };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 20000
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    })?.then((res: any) => PromotionData(res));
  }

  static checkBillingPermission({ termId, isSuccessScreen }: any) {
    const url = ConfigApi.billing.checkBillingPermission;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      next_package_id: termId,
      is_success_screen: isSuccessScreen ? 1 : 0,
      platform: mobileCheck() ? PLATFORM.MOBILE_WEB : PLATFORM.WEB
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    })?.then((res: any) => {
      const data = res?.data || {};
      return BillingPermission({ data });
    });
  }

  static checkBillingPermission2({ termId, isSuccessScreen }: any) {
    const url = ConfigApi.billing.checkBillingPermission2;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      next_package_id: termId,
      is_success_screen: isSuccessScreen ? 1 : 0
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    })?.then((res: any) => {
      const data = res?.data || {};
      return BillingPermission({
        data: {
          ...data,
          success: res?.success
        }
      });
    });
  }

  static checkPermissionDaily() {
    const url = ConfigApi.billing.checkPermissionDaily;
    const method = ConfigApi.METHOD.POST;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      const data = res?.data || {};
      return BillingPermission({ data });
    });
  }
  static setTemporaryData({ id, partnerName, query }: any): Promise<any> {
    if (id) {
      const url = parseUrlString(ConfigApi.billing.temporaryData, 'id', id);
      const method = ConfigApi.METHOD.PUT;
      const params: any = {
        partner_name: partnerName,
        value: JSON.stringify({ query: query || {} })
      };
      return AxiosClient.executeWithCache({
        url,
        method,
        params
      });
    }
    return Promise.resolve(null);
  }

  static getTemporaryData({ id }: any) {
    if (!id) return null;
    const url = parseUrlString(ConfigApi.billing.temporaryData, 'id', id);
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      const value = JSON.parse(res?.data?.result?.value || '{}');
      return {
        ...(res?.data?.result || {}),
        value
      };
    });
  }

  static checkFirstPay({ userId }: any) {
    const url = parseUrlString(ConfigApi.billing.checkFirstPay, 'userID', userId);
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      const data = res?.data?.result;
      return {
        isFirstPay: data?.is_first_pay === 1,
        code: res?.data?.code
      };
    });
  }

  static checkReferralValid(code: any) {
    const url = ConfigApi.referralValid;
    const method = ConfigApi.METHOD.GET;
    const params: any = {
      referral_code: code
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    })?.then((res: any) => {
      const data = res?.data?.result;
      return {
        error: res?.data?.error,
        isValid: data?.is_valid === 1,
        success: res?.success
      };
    });
  }

  static getReferralApply({ transactionId, referralCode }: any) {
    const url = ConfigApi.referralApply;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      transaction_id: transactionId || '',
      referral_code: referralCode || ''
    };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    })?.then((res: any) => ({
      success: res?.success
    }));
  }

  // Create transaction for TVOD
  static tvodTransaction({ tvodProductId, paymentService, tokenId }: any) {
    const method = ConfigApi.METHOD.POST;
    const url = ConfigApi.tvod.tvodTransaction;
    const params: any = {
      api_version: 1,
      tvod_product_id: tvodProductId || '',
      payment_service: paymentService || ''
    };
    if (tokenId) params.token_id = tokenId;
    const config = {
      headers: { 'Content-Type': 'application/json' },
      timeout: 30000
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    })?.then((res: any) => ({
      amountNum: res?.data?.result?.amount_num || 0,
      currency: res?.data?.result?.currency || 0,
      expiresIn: res?.data?.result?.expires_in || 0,
      orderId: res?.data?.result?.order_id || 0,
      payMethod: res?.data?.result?.pay_method || 0,
      payService: res?.data?.result?.pay_service || 0,
      payUrl: res?.data?.result?.pay_url || 0,
      userId: res?.data?.result?.user_id || 0,
      errorCode: res?.data?.error_code,
      errorMessage: res?.data?.error_message || '',
      success: res?.success || ''
    }));
  }

  // Create order transactionPay for TVOD
  static tvodTransactionPay({ orderId, bankCode, returnUrl, cancelUrl }: any) {
    const method = ConfigApi.METHOD.POST;
    const url = ConfigApi.tvod.tvodTransactionPay;
    const params: any = {
      api_version: 1,
      order_id: orderId || ''
    };
    if (bankCode) params.vnpay_bank_code = bankCode;
    if (returnUrl) params.return_url = returnUrl;
    if (cancelUrl) params.cancel_url = cancelUrl;
    const config = {
      headers: { 'Content-Type': 'application/json' },
      timeout: 30000
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    })?.then((res: any) => {
      const zalopay = ZaloPayTransaction(res);
      const shopeepay = ShopeePayTransaction(res);
      const moca = MocaTransaction(res);
      const viettelPay = ViettelPayTransaction(res);
      const napas = NapasTransaction(res);
      const vnpay = VNPayTransaction(res);
      const momo = MomoTransaction(res);
      return {
        zalopay,
        shopeepay,
        moca,
        napas,
        vnpay,
        viettelPay,
        momo,
        success: res?.success,
        errorCode: res?.data?.error_code,
        errorMessage: res?.data?.error_message || ''
      };
    });
  }

  // Get info transaction from services for TVOD
  static tvodCheckTransaction({ orderId }: any) {
    const method = ConfigApi.METHOD.GET;
    const url = replaceKey(ConfigApi.tvod.tvodCheckTransaction, 'orderId', orderId);
    const params: any = {
      api_version: 1
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    })?.then((res: any) => {
      const errorCode = res?.data?.error_code;
      let status = res?.data?.result?.status || 0;
      if (errorCode === ERROR_CODE.CODE_104) {
        status = 2;
      }
      return {
        consumingDurMsg: res?.data?.result?.consuming_dur_msg,
        durationMsg: res?.data?.result?.duration_msg,
        errorCodeMsg: res?.data?.result?.error_code_msg,
        errorDescMsg: res?.data?.result?.error_desc_msg,
        productNameMsg: res?.data?.result?.product_name_msg,
        startDate: res?.data?.result?.start_date,
        startDateMsg: Moment(new Date(res?.data?.result?.start_date)).format('DD/MM/YYYY'),
        status,
        totalPriceMsg: res?.data?.result?.total_price_msg,
        txnId: res?.data?.result?.txn_id,
        waitingDurMsg: res?.data?.result?.waiting_dur_msg,
        success: !!res?.success,
        errorCode,
        errorMessage: res?.data?.error_message,
        paymentType:
          res?.data?.error_code === ERROR_CODE.CODE_105 ? PAYMENT_TYPE.SVOD : PAYMENT_TYPE.TVOD
      };
    });
  }

  static getTVodInfo({
    contentId,
    contentType,
    accessToken,
    profileToken,
    isSimulcast,
    isLiveEvent,
    eventData
  }: any) {
    let uri = ConfigApi.tvod.tVodShowInfo;
    if (contentType === CONTENT_TYPE.MOVIE) {
      uri = ConfigApi.tvod.tVodMovieInfo;
    }
    if (isSimulcast) {
      uri = ConfigApi.tvod.tVodSimulcastInfo;
    }
    if (isLiveEvent) {
      uri = ConfigApi.tvod.tVodLiveEventInfo;
    }
    const url = replaceKey(uri, 'contentId', contentId);
    const idType = parseTVodIdType({ type: contentType });
    const method = ConfigApi.METHOD.GET;
    const params: any = {
      api_version: 1,
      id_type: idType
    };
    return AxiosClient.executeWithCache({
      url,
      params,
      method,
      accessToken,
      profileToken
    })?.then((res: any) => {
      const tVodInfo = TVodInfo(res?.data?.result, eventData);
      return {
        ...tVodInfo,
        success: res?.success,
        errorCode: res?.data?.error_code
      };
    });
  }

  static getTVodOffer({ tvodProductId, promotionCode }: any) {
    const uri = ConfigApi.tvod.tVodOffer;
    const url = replaceKey(uri, 'tvodProductId', tvodProductId);
    const method = ConfigApi.METHOD.GET;
    const params: any = {
      api_version: 1,
      promotion_code: promotionCode || ''
    };
    return AxiosClient.executeWithCache({
      url,
      params,
      method
    })?.then((res: any) => {
      const tVodOffer = TVodOffer(res?.data?.result);
      return {
        ...tVodOffer,
        success: res?.success,
        tvodProductId
      };
    });
  }

  // Get PVOD Info
  static getPVodInfo({ contentId, contentType, accessToken, profileToken }: any) {
    // const idType = parseTVodIdType({ type: contentType });
    const uri = replaceKey(ConfigApi.pvod.pVodShowInfo, 'contentId', contentId);
    const url = replaceKey(uri, 'contentType', contentType);
    const method = ConfigApi.METHOD.GET;
    const params: any = {
      api_version: 1
    };
    return AxiosClient.executeWithCache({
      url,
      params,
      method,
      accessToken,
      profileToken
    })?.then((res: any) => {
      const pVodInfo = PVodInfo(res?.data?.result);
      return {
        ...pVodInfo,
        success: res?.success,
        errorCode: res?.data?.error_code
      };
    });
  }

  // Get PVOD Offer
  static getPVodOffer({ pvodProductId, promotionCode }: any) {
    const uri = ConfigApi.pvod.pVodOffer;
    const url = replaceKey(uri, 'pvodProductId', pvodProductId);
    const method = ConfigApi.METHOD.GET;
    const params: any = {
      api_version: 1,
      promotion_code: promotionCode || ''
    };
    return AxiosClient.executeWithCache({
      url,
      params,
      method
    })?.then((res: any) => {
      const pVodOffer = PVodOffer(res?.data?.result);
      return {
        ...pVodOffer,
        success: res?.success,
        pvodProductId
      };
    });
  }
  static getPackageDiscount() {
    const url = ConfigApi.billing.packageDiscount;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    });
  }
  static getPackageDiscountOEM({ params }: any) {
    const url = ConfigApi.billing.packageDiscount;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    });
  }

  static getCampaign({ isMobile }: any) {
    const url = ConfigApi.cake.getCampaign;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      const data = res?.data?.result?.items;
      return (data || [])
        ?.filter(
          (item: any) =>
            item?.platforms?.[0]?.name === (isMobile ? PLATFORM.MOBILE_WEB : PLATFORM.WEB)
        )
        .slice(0, 3)
        .map((item: any) => ({
          displayText: item?.platforms?.[0]?.display_text,
          imageUrl: item?.platforms?.[0]?.image_url,
          name: item?.platforms?.[0]?.name,
          redirectLink: item?.platforms?.[0]?.redirect_link,
          packageGroupIds: item?.package_group_ids
        }));
    });
  }

  // Create transaction for PVOD
  static pvodTransaction({ pvodProductId, paymentService, tokenId }: any) {
    const method = ConfigApi.METHOD.POST;
    const url = ConfigApi.pvod.pvodTransaction;
    const params: any = {
      api_version: 1,
      product_id: pvodProductId || '',
      payment_service: paymentService || ''
    };
    if (tokenId) params.token_id = tokenId;
    const config = {
      headers: { 'Content-Type': 'application/json' },
      timeout: 30000
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    })?.then((res: any) => ({
      amountNum: res?.data?.result?.amount_num || 0,
      currency: res?.data?.result?.currency || 0,
      expiresIn: res?.data?.result?.expires_in || 0,
      orderId: res?.data?.result?.order_id || 0,
      payMethod: res?.data?.result?.pay_method || 0,
      payService: res?.data?.result?.pay_service || 0,
      payUrl: res?.data?.result?.pay_url || 0,
      userId: res?.data?.result?.user_id || 0,
      errorCode: res?.data?.error_code,
      errorMessage: res?.data?.error_message || '',
      success: res?.success || ''
    }));
  }

  // Create order transactionPay for PVOD
  static pvodTransactionPay({ orderId, bankCode, returnUrl, cancelUrl }: any) {
    const method = ConfigApi.METHOD.POST;
    const url = ConfigApi.pvod.pvodTransactionPay;
    const params: any = {
      api_version: 1,
      order_id: orderId || ''
    };
    if (bankCode) params.vnpay_bank_code = bankCode;
    if (returnUrl) params.return_url = returnUrl;
    if (cancelUrl) params.cancel_url = cancelUrl;
    const config = {
      headers: { 'Content-Type': 'application/json' },
      timeout: 30000
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config
    })?.then((res: any) => {
      const zalopay = ZaloPayTransaction(res);
      const shopeepay = ShopeePayTransaction(res);
      const moca = MocaTransaction(res);
      const viettelPay = ViettelPayTransaction(res);
      const napas = NapasTransaction(res);
      const vnpay = VNPayTransaction(res);
      const momo = MomoTransaction(res);
      const asiapay = AsiaPayTransaction(res);
      return {
        zalopay,
        shopeepay,
        moca,
        napas,
        vnpay,
        viettelPay,
        momo,
        asiapay,
        success: res?.success,
        errorCode: res?.data?.error_code,
        errorMessage: res?.data?.error_message || ''
      };
    });
  }

  // Get info transaction from services for PVOD
  static pvodCheckTransaction({ orderId }: any) {
    const method = ConfigApi.METHOD.GET;
    const url = replaceKey(ConfigApi.pvod.pvodCheckTransaction, 'orderId', orderId);
    const params: any = {
      api_version: 1
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params
    })?.then((res: any) => {
      const errorCode = res?.data?.error_code;
      let status = res?.data?.result?.status || 0;
      if (errorCode === ERROR_CODE.CODE_104) {
        status = 2;
      }
      return {
        consumingDurMsg: res?.data?.result?.consuming_dur_msg,
        durationMsg: res?.data?.result?.duration_msg,
        contentMsg: res?.data?.result?.content_msg,
        errorCodeMsg: res?.data?.result?.error_code_msg,
        errorDescMsg: res?.data?.result?.error_desc_msg,
        productNameMsg: res?.data?.result?.product_name_msg,
        startDate: res?.data?.result?.start_date,
        startDateMsg: Moment(new Date(res?.data?.result?.start_date)).format('DD/MM/YYYY'),
        status,
        totalPriceMsg: res?.data?.result?.total_price_msg,
        txnId: res?.data?.result?.txn_id,
        waitingDurMsg: res?.data?.result?.waiting_dur_msg,
        success: !!res?.success,
        errorCode,
        errorMessage: res?.data?.error_message,
        paymentType:
          res?.data?.error_code === ERROR_CODE.CODE_105 ? PAYMENT_TYPE.TVOD : PAYMENT_TYPE.PVOD
      };
    });
  }
}

export default PaymentApi;
