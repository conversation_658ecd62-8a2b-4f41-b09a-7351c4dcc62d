import ConfigApi from '@config/ConfigApi';
import { API_METHOD } from '@constants/constants';
import AxiosClient from './axiosClient';

class AccessTradeApi {
  static createConversion({ trackingId, transactionId, type }: any) {
    return AxiosClient.executeWithCache({
      url: ConfigApi.accessTrade.createConversion,
      method: API_METHOD.POST,
      config: {
        headers: {
          'Content-Type': 'application/json'
        }
      },
      params: {
        tracking_id: trackingId,
        transaction_id: transactionId,
        type: type || 'pay'
      }
    })?.then((res: any) => res);
  }
  static updateUtmSource({ utmSource, transactionId }: any) {
    return AxiosClient.executeWithCache({
      url: ConfigApi.accessTrade.updateUtmSource,
      method: API_METHOD.PATCH,
      config: {
        headers: {
          'Content-Type': 'application/json'
        }
      },
      params: {
        order_id: transactionId,
        utm_source: utmSource
      }
    })?.then((res: any) => res);
  }
}

export default AccessTradeApi;
