import ConfigApi from '@config/ConfigApi';
import AxiosClient from '@apis/axiosClient';

export const aiActiveThirdTracking = ({ vadeUrl, events, impressionURLTemplates }: any) => {
  const method = ConfigApi.METHOD.GET;
  if (vadeUrl) {
    AxiosClient.executeWithCache({
      url: vadeUrl,
      method
    });
  }
  if (events && events.length > 0) {
    events.forEach((event: any) => {
      if (!event?.value) return;
      AxiosClient.executeWithCache({
        url: event?.value,
        method
      });
    });
  }
  if (impressionURLTemplates && impressionURLTemplates.length > 0) {
    impressionURLTemplates.forEach((impressionURLTemplate: any) => {
      if (!impressionURLTemplate?.value) return;
      AxiosClient.executeWithCache({
        url: impressionURLTemplate?.value,
        method
      });
    });
  }
};
