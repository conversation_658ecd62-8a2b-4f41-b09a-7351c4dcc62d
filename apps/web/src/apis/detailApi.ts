import axios from 'axios';
import ConfigApi from '@config/ConfigApi';
import { parseEpisodeRange, parseVttItem } from '@services/detailServices';
import { getALTSEOImage, parseConfigParams, parseUrlString, setUrlParams } from '@helpers/common';
import ContentDetail from '@models/contentDetail';
import Ribbon from '@models/ribbon';
import CardItem from '@models/CardItem';
import { API_METHOD, PERMISSION } from '@constants/constants';
import { DRM } from '@constants/player';
import { TEXT } from '@constants/text';
import LiveTVApi from '@apis/liveTVApi';
import { setSessionPlay } from '@actions/detail';
import { LinkPlaysToRetry } from '@models/subModels';
import isEmpty from 'lodash/isEmpty';
import AxiosClient from './axiosClient';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import { ACTION_TYPE, createAction } from '@actions/actionType';
import { mapAdsToStore } from '@helpers/utils';

class DetailApi {
  static getContentById({ contentId, isGlobal }: any) {
    ConfigLocalStorage.set('currentContentId', contentId);
    const method = API_METHOD.GET;
    const url = parseUrlString(ConfigApi.cm.contentById, 'contentId', contentId);
    return AxiosClient.executeWithCache({ url, method })?.then((res: any) => {
      const data = new CardItem({ ...res.data, ribbonType: res?.data?.type, isGlobal });
      res.data = { ...data };
      return res;
    });
  }
  static getContentBySlug({
    slug,
    accessToken,
    profileToken,
    ssr,
    ipAddress,
    userAgent,
    isGlobal,
    origin
  }: any) {
    const method = API_METHOD.POST;
    const url = `${ConfigApi.cm.contentBySlug}`;
    const params: any = { entity_slug: slug };
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      accessToken,
      profileToken,
      ssr,
      config
    })?.then((res: any) => {
      const data = new CardItem({ ...res.data, isMasterBanner: true, isGlobal });
      res.data = { ...data };
      return res;
    });
  }
  static getContentDetail({
    contentId,
    episodeId,
    content,
    isCheckPermission,
    dispatch,
    isCard
  }: any) {
    ConfigLocalStorage.set('currentContentId', contentId);
    const method = API_METHOD.GET;
    const url = parseUrlString(
      parseUrlString(ConfigApi.cm.contentDetail, 'contentId', contentId),
      'episodeId',
      episodeId
    );
    return AxiosClient.executeWithCache({ url, method }).then(async (res) => {
      if (res && res.data) {
        const { id, group_id, content_concurrent_group, permission, ads } = res?.data || {};

        if (ads?.length > 0 && dispatch) {
          const adData = mapAdsToStore(ads);
          dispatch(createAction(ACTION_TYPE.SET_OUT_STREAM_ADS, adData));
        }
        const linkPlaysToRetry = LinkPlaysToRetry(res?.data);
        let concurrentScreen = '';
        if (
          permission === PERMISSION.CAN_WATCH &&
          content_concurrent_group &&
          !isCard &&
          !isEmpty(linkPlaysToRetry)
        ) {
          concurrentScreen = await dispatch(
            setSessionPlay({
              titleId: id,
              seasonId: group_id,
              episodeId: id,
              titleName: content?.title,
              contentConcurrentGroup: content_concurrent_group
            })
          );
        }
        const contentDetail: any = new ContentDetail({
          ...res.data,
          content,
          httpCode: res?.httpCode,
          concurrentScreen
        });
        if (
          ((contentDetail?.drmServiceName || '').toUpperCase() === DRM.HBO ||
            (contentDetail?.drmServiceName || '').toUpperCase() === DRM.QNET) &&
          contentDetail?.permission === PERMISSION.CAN_WATCH &&
          !isCheckPermission
        ) {
          return LiveTVApi.getQNetInfo({ contentId: contentDetail?.id, type: 'vod' }).then(
            (qnetInfo) => {
              contentDetail.qnetInfo = qnetInfo;
              res.data = contentDetail;
              return res;
            }
          );
        }
        res.data = contentDetail;
      }
      return res;
    });
  }

  static getEpisodeListBySlug = async ({
    slug,
    page,
    limit,
    accessToken,
    profileToken,
    content,
    ssr,
    ipAddress,
    userAgent,
    isGlobal,
    origin
  }: any) => {
    if (!slug) return;
    const method = API_METHOD.POST;
    let url = setUrlParams(ConfigApi.cm.episodesBySlug, { page: page || 0, limit: limit || 100 });
    url = url?.href;
    const params: any = { entity_slug: slug };
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    const getEpisodeData = await AxiosClient.executeWithCache({
      url,
      method,
      params,
      accessToken,
      profileToken,
      ssr,
      config
    });

    const data = {
      ...getEpisodeData?.data,
      items: (getEpisodeData?.data?.items || []).map(
        (item: any, index: any) =>
          new CardItem(
            {
              ...item,
              seasonName: content?.title || '',
              isEpisode: true,
              isGlobal
            },
            index
          )
      )
    };
    return data;
  };
  static getTipData({ content_id, eps_id }: any) {
    const method = API_METHOD.GET;
    const url = parseUrlString(ConfigApi.cm.tipData, 'content_id', content_id);
    const params: any = { eps_id };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => res);
  }

  static getRecommendVod({
    slug,
    page,
    limit,
    accessToken,
    profileToken,
    ssr,
    ipAddress,
    userAgent,
    isGlobal,
    origin
  }: any) {
    const pageRecommend = page || 0;
    const limitRecommend = limit || 30;
    const method = API_METHOD.POST;
    const url = `${ConfigApi.cm.contentRecommendVod}`;
    const params: any = { entity_slug: slug, page: pageRecommend, limit: limitRecommend };
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      accessToken,
      profileToken,
      ssr,
      config
    })?.then((res: any) => {
      let recommendData = null;
      if (res && res.data) {
        // add  SEO ALT IMAGE
        if (res && res?.data?.items) {
          res.data = getALTSEOImage(res.data);
        }
        recommendData = new Ribbon({
          data: {
            ...res.data,
            items: (res.data?.items || []).map(
              (item: any, index: any) => new CardItem({ ...item, isGlobal }, index)
            )
          },
          title: TEXT.RECOMMEND_TO_YOU
        });
      }
      return { ...res, recommendData };
    });
  }

  static getRelatedVod({
    slug,
    page,
    limit,
    accessToken,
    profileToken,
    ssr,
    ipAddress,
    userAgent,
    isGlobal,
    origin
  }: any) {
    const method = API_METHOD.POST;
    const pageRelated = page || 0;
    const limitRelated = limit || 30;
    const url = `${ConfigApi.cm.contentRelated}`;
    const params: any = { entity_slug: slug, page: pageRelated, limit: limitRelated };
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      accessToken,
      profileToken,
      ssr,
      config
    })?.then((res: any) => {
      let relatedData = null;
      if (res && res.data) {
        // add  SEO ALT IMAGE
        if (res && res?.data?.items) {
          res.data = getALTSEOImage(res.data);
        }
        relatedData = new Ribbon({
          data: {
            ...res.data,
            items: (res.data?.items || []).map(
              (item: any, index: any) => new CardItem({ ...item, isGlobal }, index)
            )
          },
          title: TEXT.RELATED_VIDEO
        });
      }
      return { ...res, relatedData };
    });
  }
  static getCommentList({ contentId, page, limit, ipAddress, userAgent, origin }: any) {
    const method = API_METHOD.POST;
    const url = `${parseUrlString(ConfigApi.cm.comments, 'contentId', contentId)}?page=${
      page || 0
    }&limit=${limit || 20}`;
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({ url, method, config })?.then((res: any) => res);
  }
  static postComment({ contentId, message }: any) {
    const url = parseUrlString(ConfigApi.cm.postComment, 'contentId', contentId);
    const method = API_METHOD.POST;
    const params: any = { message };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => res);
  }
  static updateRating(contentId: any, userRating: any) {
    let url = parseUrlString(ConfigApi.cm.detail.updateRating, 'contentId', contentId);
    url = parseUrlString(url, 'userRating', userRating);
    const method = API_METHOD.POST;
    return AxiosClient.executeWithCache({ url, method })?.then((res: any) => res);
  }

  static getEpisodeRange({ contentId }: any) {
    const url = parseUrlString(ConfigApi.cm.detail.episodeRange, 'contentId', contentId);
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({ url, method })?.then((res: any) => {
      if (res && res?.data && res?.data?.items) {
        const episodeRanges = parseEpisodeRange(res?.data?.items);
        return episodeRanges;
      }
      return res;
    });
  }

  static getVtt(url: any) {
    const indexOfSlash = (url || '').lastIndexOf('/');
    const domain = (url || '').substring(0, indexOfSlash + 1);
    const axiosInstance = axios.create({
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json'
      },
      timeout: 10000
    });
    return axiosInstance
      .request({ url })
      ?.then((res: any) => {
        const data = res?.data || '';
        const items = data.split(/[\r\n][\r\n]/i);
        // Loop through all captions

        const metadata: any = [];
        (items || []).forEach((item: any) => {
          const lineItems = (item || '').split('\n');
          if ((lineItems || []).length > 1) {
            lineItems.forEach(() => {
              const metaItem = parseVttItem(lineItems, 0, domain);
              metadata.push(metaItem);
            });
          }
        });
        return metadata;
      })
      .catch(() => []);
  }

  static getEpisodeListById({ contentId, page, limit, isGlobal }: any) {
    const method = API_METHOD.GET;
    let url = setUrlParams(parseUrlString(ConfigApi.cm.episodesById, 'contentId', contentId), {
      page: page || 0,
      limit: limit || 30
    });
    url = url?.href;
    return AxiosClient.executeWithCache({ url, method })?.then((res: any) => {
      const data = {
        ...res?.data,
        items: (res?.data?.items || []).map(
          (item: any, index: any) => new CardItem({ ...item, isEpisode: true, isGlobal }, index)
        )
      };
      return data;
    });
  }
}

export default DetailApi;
