import { isMobile } from 'react-device-detect';
import { API_METHOD, PLATFORM } from '@constants/constants';
import ConfigApi from '@config/ConfigApi';
import { parseUrlString } from '@helpers/common';
import { Transaction } from '@models/subModels';
import AxiosClient from './axiosClient';

class PaymentV2Api {
  static createTransaction({
    paymentMethod,
    packageId,
    promotionCode,
    tokenId,
    paymentService,
    redirectUri,
    returnUrl,
    cancelUrl
  }: any) {
    const method = API_METHOD.POST;
    const params: any = {
      payment_method: paymentMethod,
      payment_service: paymentService,
      redirect_uri: redirectUri || undefined,
      package_id: String(packageId),
      promotion_code: promotionCode,
      token_id: tokenId,
      return_url: returnUrl,
      cancel_url: cancelUrl
    };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 20000
    };
    return AxiosClient.executeWithCache({
      url: ConfigApi.billing.v2.createTransaction,
      method,
      config,
      params
    })?.then((res: any) => res);
  }
  static getInfoTransaction({ orderId, returnUrl }: any) {
    const method = API_METHOD.POST;
    const params: any = {
      order_id: orderId,
      return_url: returnUrl
    };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    };
    return AxiosClient.executeWithCache({
      url: ConfigApi.billing.v2.getInfoTransaction,
      method,
      config,
      params
    })?.then((res: any) => res);
  }
  static getResultTransaction({
    data,
    checksum,
    orderId,
    paymentMethod,
    paymentService,
    state,
    code,
    error,
    errorDescription,
    redirectUri,
    vpOrderId,
    errorCodeVp,
    merchantCodeVp,
    paymentStatusVp,
    transAmountVp,
    checkSumVp,
    billCodeVp,
    custMsisdnVp
  }: any) {
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    let params = null;
    if (vpOrderId) {
      params = {
        payment_method: paymentMethod,
        order_id: vpOrderId,
        result: {
          error_code: errorCodeVp,
          merchant_code: merchantCodeVp,
          order_id: vpOrderId,
          payment_status: paymentStatusVp,
          trans_amount: transAmountVp,
          check_sum: checkSumVp,
          billcode: billCodeVp,
          cust_msisdn: custMsisdnVp
        }
      };
    } else if (orderId) {
      params = {
        payment_method: paymentMethod,
        order_id: orderId,
        result: {
          data,
          checksum
        }
      };
    } else if (state) {
      params = {
        platform: isMobile ? PLATFORM.MOBILE_WEB : PLATFORM.WEB,
        payment_method: paymentMethod,
        payment_service: paymentService,
        result: {
          data: {
            state,
            redirect_uri: redirectUri,
            code: code || undefined,
            error: error || undefined,
            error_description: errorDescription || undefined
          }
        }
      };
    }
    return AxiosClient.executeWithCache({
      url: parseUrlString(
        ConfigApi.billing.v2.getResultTransaction,
        'orderId',
        orderId || state || vpOrderId
      ),
      method,
      config,
      params
    })?.then((res: any) => res);
  }

  static getStatusTransaction({ orderId }: any) {
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({
      url: parseUrlString(ConfigApi.billing.v2.getStatusTransaction, 'orderId', orderId),
      method
    })?.then((res: any) => {
      const transactionResult = Transaction({
        data: { ...res?.data, ...res?.data?.result },
        success: res?.success
      });
      return transactionResult;
    });
  }
  static getListTokensSaved({ paymentMethod, paymentService }: any) {
    const url = ConfigApi.billing.v2.getListTokensSaved;
    const method = ConfigApi.METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      config,
      params: {
        payment_method: paymentMethod,
        payment_service: paymentService
      }
    })?.then((res: any) => res);
  }
}

export default PaymentV2Api;
