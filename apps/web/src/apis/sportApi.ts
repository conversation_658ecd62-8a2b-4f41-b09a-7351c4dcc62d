import ConfigApi from '@config/ConfigApi';
import { customScheduleSport } from '@helpers/common';
import AxiosClient from './axiosClient';

const getMatches = ({ competitionCode }: any) => {
  const url = `${ConfigApi.sport.getMatch}?competition_code=${competitionCode}`;
  const method = ConfigApi.METHOD.GET;
  return AxiosClient.executeWithCache({ url, method })?.then((res: any) => {
    const groupMatches = customScheduleSport(res?.data?.items);
    return { data: { items: groupMatches } };
  });
};
const getRanking = ({ competitionCode }: any) => {
  const url = `${ConfigApi.sport.getRanking}?competition_code=${competitionCode}`;
  const method = ConfigApi.METHOD.GET;
  return AxiosClient.executeWithCache({ url, method });
};

export { getMatches, getRanking };
