import { API_METHOD } from '@constants/constants';
import ConfigApi from '@config/ConfigApi';
import { parseUrlString } from '@helpers/common';
import CardItem from '@models/CardItem';
import AxiosClient from './axiosClient';

class LiveStreamApi {
  static checkStatus(id: any) {
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({
      url: parseUrlString(ConfigApi.cm.liveStream.checkStatus, 'id', id),
      method
    })?.then((res: any) => res.data);
  }

  static eventRelated({ id }: any) {
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({
      url: parseUrlString(ConfigApi.cm.liveStream.eventRelated, 'id', id),
      method
    })?.then((res: any) => {
      const items = (res?.data?.items || []).map((it: any) => new CardItem({ ...it })) || [];
      return { ...res?.data, items };
    });
  }
}

export default LiveStreamApi;
