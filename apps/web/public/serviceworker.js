importScripts("https://cdn.moengage.com/webpush/releases/serviceworker_cdn.min.latest.js");
const OFFLINE_URL = '/offline.html';
const CACHE_NAME = 'offline-cache-v2';
const OFFLINE_ASSETS = [OFFLINE_URL];

self.addEventListener('install', (event) => {
    event.waitUntil(caches.open(CACHE_NAME)
        .then((cache) => {
            console.log('Opened cache');
            return cache.addAll(OFFLINE_ASSETS);
        }));
    self.skipWaiting();
});

self.addEventListener('activate', (event) => {
    event.waitUntil(caches.keys().then((cacheNames) => {
        return Promise.all(cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
                return caches.delete(cacheName);
            }
        }));
    }));
    self.clients.claim();
});

self.addEventListener('fetch', (event) => {
    if (event.request.mode === 'navigate') {
        event.respondWith(fetch(event.request)
            .catch(() => {
                return caches.match(OFFLINE_URL);
            }));
    }
});
