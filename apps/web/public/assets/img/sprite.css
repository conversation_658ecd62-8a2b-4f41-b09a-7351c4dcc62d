/*
Icon classes can be used entirely standalone. They are named after their original file names.

Example usage in HTML:

`display: block` sprite:
<div class="icon-home"></div>

To change `display` (e.g. `display: inline-block;`), we suggest using a common CSS class:

// CSS
.icon {
  display: inline-block;
}

// HTML
<i class="icon icon-home"></i>
*/
.icon-arrow-left {
  background-image: url(img/sprite.png);
  background-position: -224px -43px;
  width: 11px;
  height: 21px;
}
.icon-arrow-right {
  background-image: url(img/sprite.png);
  background-position: -224px -22px;
  width: 11px;
  height: 21px;
}
.icon-camera {
  background-image: url(img/sprite.png);
  background-position: -172px -98px;
  width: 37px;
  height: 32px;
}
.icon-heart-action-active {
  background-image: url(img/sprite.png);
  background-position: 0px -100px;
  width: 62px;
  height: 62px;
}
.icon-heart-action {
  background-image: url(img/sprite.png);
  background-position: -172px 0px;
  width: 52px;
  height: 52px;
}
.icon-heart-push {
  background-image: url(img/sprite.png);
  background-position: -172px -52px;
  width: 46px;
  height: 46px;
}
.icon-home {
  background-image: url(img/sprite.png);
  background-position: -22px -162px;
  width: 23px;
  height: 20px;
}
.icon-like-action-active {
  background-image: url(img/sprite.png);
  background-position: -100px 0px;
  width: 72px;
  height: 72px;
}
.icon-like-action {
  background-image: url(img/sprite.png);
  background-position: -118px -100px;
  width: 54px;
  height: 55px;
}
.icon-like-push {
  background-image: url(img/sprite.png);
  background-position: -62px -100px;
  width: 56px;
  height: 56px;
}
.icon-live-next {
  background-image: url(img/sprite.png);
  background-position: -224px 0px;
  width: 12px;
  height: 22px;
}
.icon-live-prev {
  background-image: url(img/sprite.png);
  background-position: -209px -98px;
  width: 12px;
  height: 22px;
}
.icon-live {
  background-image: url(img/sprite.png);
  background-position: -67px -162px;
  width: 25px;
  height: 16px;
}
.icon-logout {
  background-image: url(img/sprite.png);
  background-position: -146px -72px;
  width: 25px;
  height: 20px;
}
.icon-mine {
  background-image: url(img/sprite.png);
  background-position: 0px -162px;
  width: 22px;
  height: 22px;
}
.icon-monitor {
  background-image: url(img/sprite.png);
  background-position: -16px -184px;
  width: 18px;
  height: 18px;
}
.icon-noti {
  background-image: url(img/sprite.png);
  background-position: -124px -72px;
  width: 22px;
  height: 24px;
}
.icon-play-main {
  background-image: url(img/sprite.png);
  background-position: -172px -130px;
  width: 32px;
  height: 32px;
}
.icon-play-ttl {
  background-image: url(img/sprite.png);
  background-position: 0px -184px;
  width: 16px;
  height: 22px;
}
.icon-play-vod {
  background-image: url(img/sprite.png);
  background-position: 0px 0px;
  width: 100px;
  height: 100px;
}
.icon-setting {
  background-image: url(img/sprite.png);
  background-position: -100px -72px;
  width: 24px;
  height: 24px;
}
.icon-trailer {
  background-image: url(img/sprite.png);
  background-position: -45px -162px;
  width: 22px;
  height: 20px;
}
.icon-wach-later-large {
  background-image: url(img/sprite.png);
  background-position: -204px -130px;
  width: 20px;
  height: 20px;
}
