{"name": "@vieon/web", "version": "1.0.0", "main": "server/index.js", "author": "https://vieon.vn", "license": "VieON", "description": "Opinionated Next.js starter with Express, Redux, SASS, and Jest.", "scripts": {"start": "nodemon -w server server/index.ts", "analyze": "NEXT_PUBLIC_ANALYZE=true next build", "build:server": "tsc --project tsconfig.server.json", "build": "pnpm run clean && cross-env NEXT_PUBLIC_NODE_ENV=production next build && pnpm run build:server", "serve": "cross-env NEXT_PUBLIC_NODE_ENV=production node build/index.js", "serve-static": "npm run export && cross-env NEXT_PUBLIC_NODE_ENV=production node server-static/index.js", "export": "NEXT_PUBLIC_EXPORT_SSR=false next export -o .next/static", "export-ssr": "NEXT_PUBLIC_NODE_ENV=production NEXT_PUBLIC_EXPORT_SSR=true next export -o .next/static", "export-server": "NEXT_PUBLIC_NODE_ENV=production node server-static/index.js", "test:ci": "jest --maxWorkers=8 --ci --coverage", "test": "jest", "test-watch": "jest --watchAll", "clean": "rimraf node_modules/.cache .next", "lint:fix": "eslint --fix .", "format:fix": "prettier --write .", "check-format": "prettier --check ."}, "dependencies": {"@dailymotion/vast-client": "^6.1.0", "@floating-ui/react-dom": "2.0.2", "@floating-ui/react-dom-interactions": "0.10.3", "@next/env": "^12.3.4", "@sentry/nextjs": "7.77.0", "@vieon/analytics-node": "^1.0.0", "@vieon/core": "workspace:*", "@vieon/next-runtime-env": "^1.0.6", "@vieon/qrcode-logo": "^1.0.0", "ajv": "^8.17.1", "axios": "1.7.8", "bowser": "2.11.0", "classnames": "2.3.2", "compression": "^1.7.4", "cookie-parser": "1.4.6", "cross-env": "^7.0.3", "crypto-js": "4.2.0", "date-fns": "^4.1.0", "express": "4.18.2", "fingerprintjs2": "^2.1.4", "firebase": "10.5.2", "framer-motion": "^12.4.1", "hls.js": "1.4.12", "immer": "10.0.3", "ip": "1.1.8", "lodash": "4.17.21", "lru-cache": "10.0.1", "moment": "2.29.4", "mux.js": "6.3.0", "next": "12.3.4", "next-redux-wrapper": "4.0.1", "next-seo": "4.29.0", "nodemon": "2.0.22", "pnpm": "^10.5.2", "postcss": "^8.4.31", "prop-types": "15.8.1", "qrcode": "1.5.3", "react": "18.2.0", "react-cookies": "0.1.1", "react-datepicker": "^7.6.0", "react-device-detect": "2.2.3", "react-dom": "18.2.0", "react-google-recaptcha-v3": "^1.11.0", "react-gpt": "^2.0.1", "react-intersection-observer": "9.5.2", "react-lottie": "1.2.10", "react-popper-tooltip": "2.11.1", "react-redux": "7.2.9", "react-responsive": "^10.0.0", "react-select": "^5.10.0", "redux": "4.2.1", "redux-devtools-extension": "^2.13.9", "redux-thunk": "2.4.2", "shaka-player": "4.11.0", "swiper": "^6.8.4", "tailwind-scrollbar": "^4.0.1", "tailwindcss": "^3.4.17", "terser-webpack-plugin": "5.3.9", "ts-migrate": "^0.1.35", "ts-node": "^10.9.2", "webpack": "5.94.0", "webpack-bundle-analyzer": "^4.9.1", "xml-js": "^1.6.11"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@types/compression": "^1.8.0", "@types/cookie-parser": "^1.4.8", "@types/crypto-js": "^4.2.2", "@types/ejs": "^3.1.5", "@types/eslint": "^9.6.1", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.17", "@types/node": "^22.15.24", "@types/prettier": "^2.7.3", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.6", "@types/react-cookies": "^0.1.4", "@types/react-datepicker": "^7.0.0", "@types/react-dom": "^19.1.5", "@types/react-lottie": "^1.2.10", "@types/react-redux": "^7.1.34", "@types/webpack-bundle-analyzer": "^4.7.0", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "autoprefixer": "^10.4.20", "copy-webpack-plugin": "^11.0.0", "ejs": "^3.1.9", "eslint": "^8.55.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.33.2", "husky": "^8.0.3", "jest": "^29.7.0", "jest-cli": "^29.7.0", "lint-staged": "^12.5.0", "prettier": "^2.8.8", "rimraf": "^3.0.2", "sass": "^1.84.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "5.94.0", "webpack-bundle-analyzer": "^4.10.2"}, "engines": {"node": ">=18.16.0"}, "browserslist": [">0.3%", "not ie 11", "not dead", "not op_mini all"], "packageManager": "pnpm@10.12.4"}